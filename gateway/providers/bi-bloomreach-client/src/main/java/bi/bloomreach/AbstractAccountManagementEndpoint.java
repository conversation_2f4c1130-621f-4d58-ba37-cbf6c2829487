package bi.bloomreach;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import com.turbospaces.http.HttpProto;
import com.turbospaces.rpc.SafeJaxrsResponseConsumer;

import api.v1.AccountRoutingFlatten;
import api.v1.ApiFactory;
import api.v1.PlatformSpec;
import bi.bloomreach.data.AddPlayersRequest;
import bi.bloomreach.data.BloomreachAwardJackpotAccountFreeContributionRequest;
import bi.bloomreach.data.BloomreachMapper;
import bi.bloomreach.data.CancelFreeRoundRequest;
import bi.bloomreach.data.CreateFreeRoundRequest;
import bi.bloomreach.data.CreateFreeRoundResponse;
import bi.bloomreach.data.CreatePromotionRequest;
import bi.bloomreach.data.PreferencesRequest;
import bi.bloomreach.data.PromotionsToAccountsRequest;
import bi.bloomreach.data.RandomRewardInstanceCompleteRequest;
import bi.bloomreach.data.RandomRewardInstancesCountResponse;
import bi.bloomreach.data.RandomRewardInstancesRequest;
import bi.bloomreach.data.RandomRewardInstancesResponse;
import bi.bloomreach.data.RandomRewardRequest;
import bi.bloomreach.data.RemovePlayersRequest;
import bi.bloomreach.data.RewardRequest;
import bi.bloomreach.data.SegmentFetchRequest;
import bi.bloomreach.data.SegmentFetchResponse;
import bi.bloomreach.data.SegmentRequest;
import bi.bloomreach.data.UpdatePromotionRequest;
import bi.trustpilot.TrustpilotClient;
import bi.trustpilot.data.TrustpilotEmailInvitationRequest;
import gamehub.GameHubServiceApi;
import gamehub.api.v1.CancelAccountFreeSpinRequest;
import gamehub.api.v1.CancelFreeSpinCampaignRequest;
import gamehub.api.v1.CompleteMiniGameRewardRequestWithoutIdentity;
import gamehub.api.v1.CreateAccountFreeSpinBatchRequest;
import gamehub.api.v1.CreateFreeSpinCampaignRequest;
import gamehub.api.v1.CreateFreeSpinCampaignResponse;
import gamehub.api.v1.CreateRewardInstanceRequest;
import gamehub.api.v1.GetOfferTemplateCodesRequest;
import gamehub.api.v1.GetOfferTemplateCodesResponse;
import gamehub.api.v1.GetRandomRewardInstanceCodesRequest;
import gamehub.api.v1.GetRandomRewardInstanceCodesResponse;
import gamehub.api.v1.GetRandomRewardInstancesCountRequest;
import gamehub.api.v1.GetRandomRewardInstancesCountResponse;
import gamehub.api.v1.GetRandomRewardTemplateLimitsRequest;
import gamehub.api.v1.GetRandomRewardTemplateLimitsResponse;
import gamehub.api.v1.RandomRewardSource;
import gateway.AbstractApiEndpoint;
import gateway.CrmGatewayPropertiesFragement;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.netty.util.AsciiString;
import io.vavr.CheckedRunnable;
import jakarta.inject.Inject;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import payment.api.PaymentServiceApi;
import payment.api.v1.GetOfferTemplateListRequest;
import payment.api.v1.GetOfferTemplateListResponse;
import payment.api.v1.OfferTemplateAdminInfo;
import uam.api.UamServiceApi;
import uam.api.v1.AccountTags;
import uam.api.v1.AssignPromotionsRequest;
import uam.api.v1.AwardJackpotAccountFreeContributionRequest;
import uam.api.v1.FetchSegmentRequest;
import uam.api.v1.FetchSegmentResponse;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;
import uam.api.v1.RewardAccountManuallyRequest;
import uam.api.v1.RewardMode;
import uam.api.v1.SetAccountCategoryInfoRequest;
import uam.api.v1.SetAccountInfoRequest;
import uam.api.v1.UnassignPromotionsRequest;
import worker.WorkerServiceApi;
import worker.api.v1.RoutableRequest;

@Slf4j
public abstract class AbstractAccountManagementEndpoint
        extends AbstractApiEndpoint
        implements AccountManagementEndpoint, InitializingBean, DisposableBean, BeanNameAware {
    public static final String BEARER_AUTH = "Bearer";
    public static final String API_TOKEN_PARAM = "apiToken";
    public static final String FREE_SPIN_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSSSSS";
    public static final DateTimeFormatter BLOOMREACH_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    public static final DateTimeFormatter FREE_SPIN_DATE_FORMATTER = DateTimeFormatter.ofPattern(FREE_SPIN_DATE_FORMAT);
    public static final String BET_PER_LINE = "betPerLine";
    public static final String RANDOM_REWARDS_REQUEST_ID_FORMAT = "%s_%s";

    private final TrustpilotClient trustpilotClient;
    private final WorkerServiceApi workerApi;
    private final GameHubServiceApi gameHubApi;
    private final CrmGatewayPropertiesFragement fragement;
    private final PlatformExecutorService executor;
    private final PaymentServiceApi paymentServiceApi;
    private final UamServiceApi uamServiceApi;

    @Inject
    public AbstractAccountManagementEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            ApiFactory apiFactory,
            MeterRegistry meterRegistry,
            WorkerServiceApi workerApi,
            GameHubServiceApi gameHubApi,
            PaymentServiceApi paymentServiceApi,
            UamServiceApi uamServiceApi,
            TrustpilotClient trustpilotClient,
            CrmGatewayPropertiesFragement fragement) {
        super(props, apiFactory, cloud);
        this.workerApi = Objects.requireNonNull(workerApi);
        this.gameHubApi = Objects.requireNonNull(gameHubApi);
        this.paymentServiceApi = Objects.requireNonNull(paymentServiceApi);
        this.uamServiceApi = Objects.requireNonNull(uamServiceApi);
        this.trustpilotClient = Objects.requireNonNull(trustpilotClient);
        this.fragement = Objects.requireNonNull(fragement);
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
    }

    @Override
    public void setBeanName(String name) {
        executor.setBeanName(name);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();
    }

    @Override
    public void destroy() throws Exception {
        executor.destroy();
    }

    @Override
    public void reward(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, RewardRequest req) {
        if (isForbidden(headers, req.brand, async)) {
            return;
        }

        if (!req.uuids.isEmpty() && StringUtils.isEmpty(req.requestId)) {
            log.warn("Missing [Request Id] for Reward batch processing");
            async.resume(Response.notModified().build());
            return;
        }

        if ((StringUtils.isNotEmpty(req.sweepstakeAmount) || StringUtils.isNotEmpty(req.goldAmount)) && StringUtils.isNotEmpty(req.fiatAmount)) {
            async.resume(Response.status(Response.Status.BAD_REQUEST.getStatusCode(), "GC/SC or fiat expected").build());
            return;
        }

        if (StringUtils.isNotEmpty(req.uuid)) {
            req.uuids.add(req.uuid);
        }

        if (req.uuids.isEmpty()) {
            async.resume(Response.notModified().build());
            return;
        }

        List<String> reqUUIDList = Lists.newArrayList(req.uuids)
                .stream()
                .peek(uuid -> {
                    if (StringUtils.isEmpty(uuid)) {
                        log.warn("Empty UUID found in reward request, origin requestId: {}", req.requestId);
                    }
                })
                .filter(StringUtils::isNotEmpty)
                .toList();

        CompletableFuture<?>[] futures = new CompletableFuture<?>[reqUUIDList.size()];
        for (int i = 0; i < reqUUIDList.size(); i++) {
            String reqUuid = reqUUIDList.get(i);
            futures[i] = rewardAccount(reqUuid, req).toCompletableFuture();
        }
        CompletableFuture.allOf(futures).whenComplete((BiConsumer<Object, Throwable>) (ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to reward batch", err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });
    }

    @Override
    public void createRandomRewardInstance(AsyncResponse async,
                                           ChannelHandlerContext ctx,
                                           HttpHeaders headers,
                                           RandomRewardRequest req) {
        if (isForbidden(headers, req.brand, async)) {
            return;
        }
        if (!req.uuids.isEmpty() && StringUtils.isEmpty(req.requestId)) {
            log.warn("Missing [Request Id] for Random Reward batch processing");
            async.resume(Response.notModified().build());
            return;
        }
        if (req.uuids.isEmpty()) {
            async.resume(Response.notModified().build());
            return;
        }

        var offerTemplateCodesFuture = gameHubApi.getOfferTemplateCodes(
                GetOfferTemplateCodesRequest.newBuilder()
                        .setBrand(req.brand)
                        .setRandomRewardTemplateCode(req.rewardTemplateCode)
                        .build(),
                AsciiString.cached(req.rewardTemplateCode));

        offerTemplateCodesFuture.addListener(
                new SafeJaxrsResponseConsumer(offerTemplateCodesFuture, async, apiFactory) {
                    @Override
                    public void accept(ResponseWrapperFacade packedResponse) throws Throwable {
                        var status = packedResponse.status();
                        if (!status.isOK()) {
                            log.warn("Failed to get offer codes for rewardTemplateCode={}",
                                    req.rewardTemplateCode);
                            async.resume(translateError(status));
                            return;
                        }

                        var offerTemplateCodesResp = packedResponse.unpack(GetOfferTemplateCodesResponse.class);
                        var codesList = offerTemplateCodesResp.getOfferTemplateCodesList();

                        if (codesList.isEmpty()) {
                            log.debug("No offerTemplateCodes found for rewardTemplateCode={}. Continuing...",
                                    req.rewardTemplateCode);
                            proceedWithRandomRewards(req, async);
                        } else {
                            checkCodesWithPaymentService(codesList, req, async);
                        }
                    }
                });
    }

    private void checkCodesWithPaymentService(List<String> codesList,
                                              RandomRewardRequest req,
                                              AsyncResponse async) {
        var getOfferTemplateListRequest = GetOfferTemplateListRequest.newBuilder()
                .setBrandName(req.brand)
                .addAllCode(codesList)
                .build();

        var offerTemplateListFuture = paymentServiceApi.getOfferTemplateList(
                getOfferTemplateListRequest,
                AsciiString.cached(req.rewardTemplateCode));

        offerTemplateListFuture.addListener(
                new SafeJaxrsResponseConsumer(offerTemplateListFuture, async, apiFactory) {
                    @Override
                    public void accept(ResponseWrapperFacade packedResponse) throws Throwable {
                        if (!packedResponse.status().isOK()) {
                            log.warn("Failed to getOfferTemplateList for codes={} brand={}, rewardTemplateCode={}",
                                    codesList, req.brand, req.rewardTemplateCode);
                            async.resume(
                                    Response.status(Response.Status.BAD_REQUEST)
                                            .entity("Error loading offerTemplateList for brand " + req.brand)
                                            .build());
                            return;
                        }

                        var getOfferTemplateListResponse = packedResponse.unpack(GetOfferTemplateListResponse.class);

                        List<OfferTemplateAdminInfo> templates = getOfferTemplateListResponse.getOfferTemplateList();

                        if (templates.size() != codesList.size()) {
                            log.warn("Mismatch in # of templates returned. Requested={} got={}",
                                    codesList.size(), templates.size());
                            async.resume(Response.status(Response.Status.BAD_REQUEST)
                                    .entity("Some offer template codes do not exist or are missing.")
                                    .build());
                            return;
                        }

                        for (OfferTemplateAdminInfo ot : templates) {
                            if (ot.getInactive()) {
                                log.warn("OfferTemplateCode={} is INACTIVE for rewardTemplateCode={}",
                                        ot.getCode(), req.rewardTemplateCode);
                                async.resume(Response.status(Response.Status.BAD_REQUEST)
                                        .entity("OfferTemplateCode=" + ot.getCode() + " is inactive.")
                                        .build());
                                return;
                            }
                        }

                        proceedWithRandomRewards(req, async);
                    }
                });
    }

    private void proceedWithRandomRewards(RandomRewardRequest req, AsyncResponse async) {

        var apiLimitsResponse = gameHubApi.getRandomRewardTemplateLimits(
                GetRandomRewardTemplateLimitsRequest.newBuilder()
                        .setCode(req.rewardTemplateCode)
                        .build(),
                AsciiString.cached(req.rewardTemplateCode));
        apiLimitsResponse.addListener(new SafeJaxrsResponseConsumer(apiLimitsResponse, async, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade packedResponse) throws Throwable {
                var status = packedResponse.status();
                if (!status.isOK()) {
                    async.resume(
                            new RuntimeException("getRandomRewardTemplateLimits returned error: "
                                    + status.errorCode().getNumber()));
                    return;
                }

                var templateLimitsResponse = packedResponse.unpack(GetRandomRewardTemplateLimitsResponse.class);
                List<String> reqUUIDList = getValidUUIDs(req);
                handleTemplateLimits(templateLimitsResponse, reqUUIDList);

                createRandomRewardsInBackground(reqUUIDList, req, async);
            }
        });
    }

    @Override
    public void getRandomRewardInstances(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, RandomRewardInstancesRequest req) {
        if (isForbidden(headers, req.brand, async)) {
            return;
        }

        if (StringUtils.isEmpty(req.rewardTemplateCode)) {
            async.resume(Response.status(Response.Status.BAD_REQUEST)
                    .entity("Missing rewardTemplateCode to retract random rewards")
                    .build());
            return;
        }

        var wrapper = gameHubApi.getRewardInstanceCodes(
                GetRandomRewardInstanceCodesRequest.newBuilder()
                        .setRewardTemplateCode(req.rewardTemplateCode)
                        .build(),
                AsciiString.cached(req.rewardTemplateCode));

        wrapper.addListener(new AbstractSafeResponseConsumer(wrapper, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade responseWrapperFacade) throws Throwable {
                var status = responseWrapperFacade.status();
                if (!status.isOK()) {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity("getRandomRewardTemplateLimits returned error: " + status)
                            .build());
                    return;
                }

                if (responseWrapperFacade.is(GetRandomRewardInstanceCodesResponse.class)) {
                    var response = responseWrapperFacade
                            .unpack(GetRandomRewardInstanceCodesResponse.class);

                    if (response.getRewardInstanceCodeList().isEmpty()) {
                        log.info("No random reward instances found for templateCode: {}", req.rewardTemplateCode);
                        async.resume(Response.accepted().build());
                        return;
                    }

                    RandomRewardInstancesResponse rewardInstancesResponse = new RandomRewardInstancesResponse(response.getRewardInstanceCodeList());
                    async.resume(Response.ok(rewardInstancesResponse).build());

                } else {
                    async.resume(new RuntimeException("Unexpected response type while getting random reward instances"));
                }
            }
        });
    }

    @Override
    public void getRandomRewardInstancesCount(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, RandomRewardInstancesRequest req) {
        if (isForbidden(headers, req.brand, async)) {
            return;
        }

        if (StringUtils.isEmpty(req.rewardTemplateCode)) {
            async.resume(Response.status(Response.Status.BAD_REQUEST)
                    .entity("Missing rewardTemplateCode to retract random rewards")
                    .build());
            return;
        }

        var wrapper = gameHubApi.getRandomRewardInstancesCount(
                GetRandomRewardInstancesCountRequest.newBuilder()
                        .setRewardTemplateCode(req.rewardTemplateCode)
                        .build(),
                AsciiString.cached(req.rewardTemplateCode));

        wrapper.addListener(new AbstractSafeResponseConsumer(wrapper, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade responseWrapperFacade) throws Throwable {
                var status = responseWrapperFacade.status();
                if (!status.isOK()) {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity("getRandomRewardInstancesCount returned error: " + status)
                            .build());
                    return;
                }

                if (responseWrapperFacade.is(GetRandomRewardInstancesCountResponse.class)) {
                    var response = responseWrapperFacade
                            .unpack(GetRandomRewardInstancesCountResponse.class);

                    if (response.getCount() == 0L) {
                        log.info("No random reward instances found for templateCode: {}", req.rewardTemplateCode);
                        async.resume(Response.ok().build());
                        return;
                    }

                    RandomRewardInstancesCountResponse rewardInstancesResponse = new RandomRewardInstancesCountResponse(response.getCount());
                    async.resume(Response.ok(rewardInstancesResponse).build());

                } else {
                    async.resume(new RuntimeException("Unexpected response type while getting random reward instances"));
                }
            }
        });
    }

    @Override
    public void completeRandomRewardInstances(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, RandomRewardInstanceCompleteRequest req) {
        if (isForbidden(headers, req.brand, async)) {
            return;
        }

        List<String> list = Lists.newArrayList(req.rewardInstanceCodes)
                .stream()
                .filter(StringUtils::isNotEmpty)
                .toList();

        CompletableFuture<?>[] futures = new CompletableFuture<?>[list.size()];
        for (int i = 0; i < list.size(); i++) {
            String code = list.get(i);
            futures[i] = completeRandomRewardInstance(code, req.brand).toCompletableFuture();
        }
        CompletableFuture.allOf(futures).whenComplete((BiConsumer<Object, Throwable>) (ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to complete random reward instances batch", err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });

    }

    @Override
    public void segment(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, SegmentRequest req) {
        if (isForbidden(headers, req.brand, async)) {
            return;
        }

        if (StringUtils.isNotEmpty(req.uuid)) {
            req.uuids.add(req.uuid);
        }

        if (!validRequest(req)) {
            async.resume(Response.notModified().build());
            return;
        }

        long timestamp = getRequestTimestamp(headers.getRequestHeaders());

        List<String> newArrayList = Lists.newArrayList(req.uuids);
        CompletableFuture<?>[] futures = new CompletableFuture<?>[newArrayList.size()];
        for (int i = 0; i < newArrayList.size(); i++) {
            String it = newArrayList.get(i);
            futures[i] = segmentAccount(it, req, timestamp).toCompletableFuture();
        }
        CompletableFuture.allOf(futures).whenComplete((BiConsumer<Object, Throwable>) (ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to segment batch", err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });
    }

    @Override
    public void fetchSegment(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, SegmentFetchRequest req) {
        if (isForbidden(headers, req.brand, async)) {
            return;
        }

        var wrapper = uamServiceApi.fetchSegment(
                FetchSegmentRequest.newBuilder().addAllUuids(req.uuids).setBrand(req.brand).build(),
                AsciiString.cached(req.requestId));

        wrapper.addListener(new AbstractSafeResponseConsumer(wrapper, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade responseWrapperFacade) throws Throwable {
                var status = responseWrapperFacade.status();
                if (!status.isOK()) {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity("fetchSegment returned error: " + status)
                            .build());
                    return;
                }

                if (responseWrapperFacade.is(FetchSegmentResponse.class)) {
                    var response = responseWrapperFacade
                            .unpack(FetchSegmentResponse.class);

                    List<AccountTags> accountTagsList = response.getAccountTagsList();
                    if (accountTagsList.isEmpty()) {
                        log.info("No random accounts found!");
                        async.resume(Response.accepted().build());
                        return;
                    }
                    async.resume(Response.ok(fromProto(accountTagsList)).build());

                } else {
                    async.resume(new RuntimeException("Unexpected response type while fetching segments"));
                }
            }
        });

    }

    @Override
    public void preferences(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, PreferencesRequest req) {
        if (isForbidden(headers, req.brand, async)) {
            return;
        }

        if (!req.uuids.isEmpty() && StringUtils.isEmpty(req.requestId)) {
            log.warn("Missing [Request Id] for Bloomreach-Preferences batch processing");
            async.resume(Response.notModified().build());
            return;
        }

        if (StringUtils.isNotEmpty(req.uuid)) {
            req.uuids.add(req.uuid);
        }

        if (req.uuids.isEmpty()) {
            async.resume(Response.notModified().build());
            return;
        }

        List<String> reqUUIDList = Lists.newArrayList(req.uuids);
        var flattens = toValidAccountRoutingFlattens(reqUUIDList);

        if (flattens.isEmpty()) {
            async.resume(Response.notModified().build());
            return;
        }

        CompletableFuture<?>[] futures = new CompletableFuture<?>[flattens.size()];
        for (int i = 0; i < flattens.size(); i++) {
            futures[i] = preferencesAccount(flattens.get(i), req).toCompletableFuture();
        }

        CompletableFuture.allOf(futures).whenComplete((BiConsumer<Object, Throwable>) (ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to preferences batch", err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });
    }

    @Override
    public void createFreeRound(AsyncResponse async,
                                ChannelHandlerContext ctx,
                                HttpHeaders headers,
                                String provider,
                                CreateFreeRoundRequest createFreeRoundRequest) {
        String brandName = createFreeRoundRequest.brandName;
        if (isForbidden(headers, brandName, async)) {
            return;
        }

        var request = toCreateFreeSpinCampaignRequest(createFreeRoundRequest, provider);
        var wrapper = gameHubApi.createFreeSpinCampaignRequest(request.build(),
                AsciiString.cached(createFreeRoundRequest.bonusCode));

        wrapper.addListener(new AbstractSafeResponseConsumer(wrapper, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade responseWrapperFacade) throws Throwable {
                var status = responseWrapperFacade.status();
                CreateFreeSpinCampaignResponse response;

                if (responseWrapperFacade.is(CreateFreeSpinCampaignResponse.class)) {
                    response = responseWrapperFacade.unpack(CreateFreeSpinCampaignResponse.class);

                    if (!status.isOK()) {
                        async.resume(Response.status(Response.Status.BAD_REQUEST)
                                .entity("createFreeRound returned error: %s with status: %s".formatted(response.getMsg(), status))
                                .build());
                        return;
                    }

                    async.resume(Response.ok(new CreateFreeRoundResponse(response)).build());
                } else {
                    async.resume(new RuntimeException("Unexpected response type while creating free round"));
                }
            }
        });
    }

    @Override
    public void cancelFreeRound(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers,
                                String provider,
                                CancelFreeRoundRequest cancelFreeRoundRequest) {
        String brandName = cancelFreeRoundRequest.brandName;
        if (isForbidden(headers, brandName, async)) {
            return;
        }

        String bonusCode = cancelFreeRoundRequest.bonusCode;

        executeGameApi(() -> {
            var request = CancelFreeSpinCampaignRequest.newBuilder()
                    .setBonusCode(bonusCode)
                    .setProvider(provider)
                    .setCurrency(Objects.requireNonNull(cancelFreeRoundRequest.currency))
                    .setBrandName(Objects.requireNonNull(brandName));

            executeInBackground(Any.pack(request.build()), brandName, bonusCode);
        }, async);
    }

    @Override
    public void addPlayers(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers,
                           String provider, AddPlayersRequest addPlayersRequest) {
        String brandName = addPlayersRequest.brandName;
        if (isForbidden(headers, brandName, async)) {
            return;
        }

        var flattens = toValidAccountRoutingFlattens(addPlayersRequest.uuids);

        executeGameApi(() -> {
            var req = toCreateAccountFreeSpinBatchRequest(addPlayersRequest, provider, flattens);
            var routingKey = addPlayersRequest.bonusCode;
            executeInBackground(Any.pack(req.build()), brandName, routingKey);
        }, async);
    }

    @Override
    public void removePlayers(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers,
                              String provider, RemovePlayersRequest removePlayersRequest) {
        String brandName = removePlayersRequest.brandName;
        if (isForbidden(headers, brandName, async)) {
            return;
        }

        var flattens = toValidAccountRoutingFlattens(removePlayersRequest.uuids);
        executeGameApi(() -> {
            var req = toCancelAccountFreeSpinRequest(removePlayersRequest, provider, flattens);
            var routingKey = removePlayersRequest.removalBonus;
            executeInBackground(Any.pack(req.build()), brandName, routingKey);
        }, async);
    }

    private void executeInBackground(Any request, String brandName, String routingKey) {
        api.v1.RoutableRequest routableRequest = api.v1.RoutableRequest.newBuilder()
                .setRequest(request)
                .setRoutingKey(routingKey)
                .build();

        gameHubApi.processInBackground(routableRequest, AsciiString.cached(brandName));
    }

    @Override
    public void awardJackpotAccountFreeContribution(
            AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, BloomreachAwardJackpotAccountFreeContributionRequest req) {
        if (isForbidden(headers, req.brand, async)) {
            return;
        }

        if (req.uuids.isEmpty()) {
            async.resume(Response.notModified().build());
            return;
        }

        List<String> reqUUIDList = Lists.newArrayList(req.uuids);
        CompletableFuture<?>[] futures = new CompletableFuture<?>[reqUUIDList.size()];
        for (int i = 0; i < reqUUIDList.size(); i++) {
            String uuid = reqUUIDList.get(i);
            futures[i] = awardJackpotFreeContribution(uuid, req).toCompletableFuture();
        }
        CompletableFuture.allOf(futures).whenComplete((BiConsumer<Object, Throwable>) (ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to Award Jackpot Account Free Contribution batch", err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });
    }

    @Override
    public void ratingEmailInvite(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, TrustpilotEmailInvitationRequest request) {
        if (isForbidden(headers, request.brand, async)) {
            return;
        }

        trustpilotClient.sendEmailInvitation(request, executor).addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(Integer result) {
                async.resume(Response.status(Response.Status.fromStatusCode(result)).build());
            }

            @Override
            public void onFailure(Throwable t) {
                Throwable rootCause = ExceptionUtils.getRootCause(t);
                log.warn(t.getMessage(), t);
                async.resume(Response.status(HttpResponseStatus.INTERNAL_SERVER_ERROR.code()).entity(rootCause.getMessage()).build());
            }
        }, MoreExecutors.directExecutor());
    }

    protected abstract void auth(String authHeader, String brand) throws Exception;

    @Override
    public void createPromotion(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, CreatePromotionRequest request) {
        if (isForbidden(headers, request.brandName, async)) {
            return;
        }
        var uamApiRequest = BloomreachMapper.toCreatePromotionRequest(request);
        RoutableRequest routableRequest = RoutableRequest.newBuilder()
                .setRequest(Any.pack(uamApiRequest.build()))
                .setRoutingKey(String.format("%s-%s", request.code, request.brandName))
                .build();
        workerApi.processInBackground(routableRequest, AsciiString.cached(request.brandName)).whenComplete((ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to create promotion '{}'", uamApiRequest, err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });
    }

    @Override
    public void updatePromotion(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, UpdatePromotionRequest request) {
        if (isForbidden(headers, request.brandName, async)) {
            return;
        }
        var uamApiRequest = BloomreachMapper.toUpdatePromotionRequest(request);
        RoutableRequest routableRequest = RoutableRequest.newBuilder()
                .setRequest(Any.pack(uamApiRequest.build()))
                .setRoutingKey(String.format("%s-%s", request.code, request.brandName))
                .build();
        workerApi.processInBackground(routableRequest, AsciiString.cached(request.brandName)).whenComplete((ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to toggle promotion '{}'", uamApiRequest, err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });
    }

    @Override
    public void assignPromotions(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, PromotionsToAccountsRequest request) {
        if (isForbidden(headers, request.brandName, async)) {
            return;
        }

        if (!validRequest(request)) {
            async.resume(Response.notModified().build());
            return;
        }

        List<String> reqUUIDList = Lists.newArrayList(request.uuids);
        CompletableFuture<?>[] futures = new CompletableFuture<?>[reqUUIDList.size()];
        for (int i = 0; i < reqUUIDList.size(); i++) {
            String uuid = reqUUIDList.get(i);
            futures[i] = assignPromotionsToAccount(uuid, request).toCompletableFuture();
        }
        CompletableFuture.allOf(futures).whenComplete((BiConsumer<Object, Throwable>) (ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to assign promotions to account batch", err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });
    }

    private CompletionStage<?> assignPromotionsToAccount(String uuid, PromotionsToAccountsRequest request) {
        AccountRoutingFlatten flatten = AccountRoutingFlatten.standard().readExternal(uuid);
        Identity identity = toIdentity(flatten);

        AssignPromotionsRequest sreqb = BloomreachMapper.toAssignPromotionsRequest(request.promotionCodes, identity);
        var req = RoutableRequest.newBuilder()
                .setRequest(Any.pack(sreqb))
                .setRoutingKey(flatten.getRoutingKey().toString());

        return workerApi.processInBackground(req.build(), AsciiString.cached(request.brandName));
    }

    @Override
    public void unassignPromotions(AsyncResponse async, ChannelHandlerContext ctx, HttpHeaders headers, PromotionsToAccountsRequest request) {
        if (isForbidden(headers, request.brandName, async)) {
            return;
        }

        if (!validRequest(request)) {
            async.resume(Response.notModified().build());
            return;
        }

        List<String> reqUUIDList = Lists.newArrayList(request.uuids);
        CompletableFuture<?>[] futures = new CompletableFuture<?>[reqUUIDList.size()];
        for (int i = 0; i < reqUUIDList.size(); i++) {
            String uuid = reqUUIDList.get(i);
            futures[i] = unassignPromotionsFromAccount(uuid, request).toCompletableFuture();
        }
        CompletableFuture.allOf(futures).whenComplete((BiConsumer<Object, Throwable>) (ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to assign promotions to account batch", err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });
    }

    private CompletionStage<?> unassignPromotionsFromAccount(String uuid, PromotionsToAccountsRequest request) {
        AccountRoutingFlatten flatten = AccountRoutingFlatten.standard().readExternal(uuid);
        Identity identity = toIdentity(flatten);

        UnassignPromotionsRequest sreqb = BloomreachMapper.toUnassignPromotionsRequest(request.promotionCodes, identity);
        var req = RoutableRequest.newBuilder()
                .setRequest(Any.pack(sreqb))
                .setRoutingKey(flatten.getRoutingKey().toString());

        return workerApi.processInBackground(req.build(), AsciiString.cached(request.brandName));
    }

    private static boolean validRequest(PromotionsToAccountsRequest request) {
        if (request.uuids.isEmpty() || request.promotionCodes.isEmpty()) {
            log.warn("Request is not valid: list of uuids empty = '{}', list of promotions empty = '{}'", request.uuids.isEmpty(),
                    request.promotionCodes.isEmpty());
            return false;
        }
        return true;
    }

    private CompletionStage<?> rewardAccount(String uuid, RewardRequest request) {
        AccountRoutingFlatten flatten = AccountRoutingFlatten.standard().readExternal(uuid);
        Identity identity = toIdentity(flatten);

        RewardAccountManuallyRequest.Builder sreqb = toRewardAccountManuallyRequest(request, identity);
        var req = RoutableRequest.newBuilder()
                .setRequest(Any.pack(sreqb.build()))
                .setRoutingKey(flatten.getRoutingKey().toString());

        return workerApi.processInBackground(req.build(), AsciiString.cached(request.brand));
    }

    private CompletionStage<?> randomRewardAccount(String uuid, RandomRewardRequest request, int counter) {
        AccountRoutingFlatten flatten = AccountRoutingFlatten.standard().readExternal(uuid);
        Identity identity = toIdentity(flatten);

        CreateRewardInstanceRequest.Builder builder = CreateRewardInstanceRequest.newBuilder();
        builder.setIdentity(identity);
        builder.setBrandName(request.brand);
        builder.setRequestId(RANDOM_REWARDS_REQUEST_ID_FORMAT.formatted(request.requestId, counter));
        builder.setRewardTemplateCode(request.rewardTemplateCode);
        builder.setSource(RandomRewardSource.BLOOMREACH);

        var req = RoutableRequest.newBuilder()
                .setRequest(Any.pack(builder.build()))
                .setRoutingKey(flatten.getRoutingKey().toString());
        return workerApi.processInBackground(req.build(), AsciiString.cached(request.brand));
    }

    private CompletionStage<?> completeRandomRewardInstance(String code, String brandName) {

        CompleteMiniGameRewardRequestWithoutIdentity.Builder builder = CompleteMiniGameRewardRequestWithoutIdentity.newBuilder();
        builder.setRewardInstanceCode(code);

        var req = RoutableRequest.newBuilder()
                .setRequest(Any.pack(builder.build()))
                .setRoutingKey(code);
        return workerApi.processInBackground(req.build(), AsciiString.cached(brandName));
    }

    static boolean validRequest(SegmentRequest req) {
        boolean hasTags = StringUtils.isNotEmpty(req.addTags) || StringUtils.isNotEmpty(req.removeTags);
        boolean hasRequestId = StringUtils.isNotEmpty(req.requestId);
        boolean hasUUID = !req.uuids.isEmpty();
        boolean allUUIDAreValid = req.uuids.stream().allMatch(AbstractAccountManagementEndpoint::validUUID);

        return hasTags && hasRequestId && hasUUID && allUUIDAreValid;
    }

    /**
     * Check is given uuid is valid bloomreach uuid. BR uuid format - List of account ids in format: {hash/id}
     *
     * @param uuid given bloomreach uuid
     * @return true in case if give uuid is valid
     */
    static boolean validUUID(String uuid) {
        boolean rez = false;
        if (StringUtils.isNotBlank(uuid)) {
            String[] keyHash = uuid.split("/");
            rez = keyHash.length == 2 && StringUtils.isAlphanumeric(keyHash[0]) && StringUtils.isNumeric(keyHash[1])
                    && StringUtils.isNotBlank(keyHash[0]);
        }
        return rez;
    }

    private CompletionStage<?> awardJackpotFreeContribution(String uuid, BloomreachAwardJackpotAccountFreeContributionRequest request) {
        AccountRoutingFlatten flatten = AccountRoutingFlatten.standard().readExternal(uuid);
        Identity identity = toIdentity(flatten);

        AwardJackpotAccountFreeContributionRequest sreqb = toAwardJackpotAccountFreeContributionRequest(request, identity);
        var req = RoutableRequest.newBuilder()
                .setRequest(Any.pack(sreqb))
                .setRoutingKey(flatten.getRoutingKey().toString());

        return workerApi.processInBackground(req.build(), AsciiString.cached(request.brand));
    }

    private static AwardJackpotAccountFreeContributionRequest toAwardJackpotAccountFreeContributionRequest(
            BloomreachAwardJackpotAccountFreeContributionRequest req, Identity identity) {
        AwardJackpotAccountFreeContributionRequest.Builder sreqb = AwardJackpotAccountFreeContributionRequest.newBuilder();
        sreqb.setIdentity(identity);
        sreqb.setCode(req.code);
        sreqb.setSweepstakeAmount(req.sweepstakeAmount);
        sreqb.setCampaignId(req.campaignId);
        sreqb.setCreditorId(req.creditorId);
        sreqb.setExpireAt(req.expireAt);
        return sreqb.build();
    }

    private CompletionStage<?> segmentAccount(String uuid, SegmentRequest request, long timestamp) {
        AccountRoutingFlatten flatten = AccountRoutingFlatten.standard().readExternal(uuid);
        Identity identity = toIdentity(flatten);

        SetAccountCategoryInfoRequest.Builder sreqb = toSetAccountCategoryRequest(request, identity, timestamp);

        return uamServiceApi.setAccountCategoryInfoInBackground(sreqb.build(), flatten.getRoutingKey(), AsciiString.cached(request.brand));
    }

    private CompletionStage<?> preferencesAccount(AccountRoutingFlatten flatten, PreferencesRequest request) {
        Identity identity = toIdentity(flatten);
        SetAccountInfoRequest.Builder sreqb = toSetAccountInfoRequest(identity, request);

        var req = RoutableRequest.newBuilder()
                .setRequest(Any.pack(sreqb.build()))
                .setRoutingKey(flatten.getRoutingKey().toString());

        return workerApi.processInBackground(req.build(), AsciiString.cached(request.brand));
    }

    private static List<Long> toAccountIdLong(List<AccountRoutingFlatten> flattens) {
        return flattens.stream()
                .map(AccountRoutingFlatten::getAccountId)
                .toList();
    }

    private ListenableFuture<?> executeGameApi(CheckedRunnable providerApiExecutor, AsyncResponse async) {
        return executor.submit(() -> {
            try {
                providerApiExecutor.run();
                async.resume(Response.status(Response.Status.OK).build());
            } catch (Throwable err) {
                Throwable rootCause = ExceptionUtils.getRootCause(err);
                log.warn(err.getMessage(), err);
                async.resume(Response.status(HttpResponseStatus.INTERNAL_SERVER_ERROR.code()).entity(rootCause.getMessage()).build());
            }
        });
    }

    private static SetAccountInfoRequest.Builder toSetAccountInfoRequest(Identity identity, PreferencesRequest bloomreachPreferencesRequest) {
        var info = uam.api.v1.AccountInfo.newBuilder();

        if (Objects.nonNull(bloomreachPreferencesRequest.doNotSendEmails)) {
            info.setDoNotSendEmails(bloomreachPreferencesRequest.doNotSendEmails);
        }
        if (Objects.nonNull(bloomreachPreferencesRequest.doNotSendSms)) {
            info.setDoNotSendSms(bloomreachPreferencesRequest.doNotSendSms);
        }
        if (Objects.nonNull(bloomreachPreferencesRequest.doNotSendPushes)) {
            info.setDoNotSendPushes(bloomreachPreferencesRequest.doNotSendPushes);
        }

        return SetAccountInfoRequest.newBuilder()
                .setIdentity(identity)
                .setInfo(info);
    }

    private static SetAccountCategoryInfoRequest.Builder toSetAccountCategoryRequest(SegmentRequest tagReq, Identity identity, long timestamp) {
        SetAccountCategoryInfoRequest.Builder sreqb = SetAccountCategoryInfoRequest.newBuilder();
        sreqb.setIdentity(identity);
        sreqb.setCategory(tagReq.segment);
        sreqb.setTimestamp(timestamp);

        if (StringUtils.isNotEmpty(tagReq.removeTags)) {
            sreqb.addAllToRemove(Splitter.on(',').omitEmptyStrings().trimResults().splitToList(tagReq.removeTags));
        }
        if (StringUtils.isNotEmpty(tagReq.addTags)) {
            sreqb.addAllToAdd(Splitter.on(',').omitEmptyStrings().trimResults().splitToList(tagReq.addTags));
        }
        return sreqb;
    }

    private static RewardAccountManuallyRequest.Builder toRewardAccountManuallyRequest(RewardRequest req, Identity identity) {
        RewardAccountManuallyRequest.Builder sreqb = RewardAccountManuallyRequest.newBuilder();

        sreqb.setIdentity(identity);
        Optional.ofNullable(req.sweepstakeAmount).ifPresent(sreqb::setSweepstakeAmount);
        Optional.ofNullable(req.goldAmount).ifPresent(sreqb::setGoldAmount);
        Optional.ofNullable(req.fiatAmount).ifPresent(sreqb::setFiatAmount);
        sreqb.setMode(RewardMode.AUTO);
        sreqb.setSkipEmail(true);
        sreqb.setCampaignId(req.campaignId);
        sreqb.setCreditorId(req.creditorId);

        if (StringUtils.isNotEmpty(req.requestId)) {
            sreqb.setSessionId(req.requestId);
        }

        return sreqb;
    }

    private static Identity toIdentity(AccountRoutingFlatten flatten) {
        IdentityByAccountId.Builder byAccountId = IdentityByAccountId.newBuilder();
        byAccountId.setAccountId(flatten.getAccountId());
        byAccountId.setPlatform(PlatformSpec.WEB.code());
        return Identity.newBuilder().setByAccountId(byAccountId).build();
    }

    private boolean isForbidden(HttpHeaders headers, String brand, AsyncResponse async) {
        boolean isValidAuth = false;
        try {
            auth(headers.getHeaderString(HttpHeaders.AUTHORIZATION), brand);
            isValidAuth = true;
        } catch (WebApplicationException err) {
            log.warn(err.getMessage(), err);
            forbiddenResponse(async, err);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            forbiddenResponse(async, e);
        }
        return !isValidAuth;
    }

    private static void forbiddenResponse(AsyncResponse async, Exception e) {
        async.resume(Response.status(Response.Status.FORBIDDEN).entity(e.getMessage()).build());
    }

    private static List<AccountRoutingFlatten> toValidAccountRoutingFlattens(List<String> uuids) {
        return uuids.stream()
                .map(uuid -> {
                    try {
                        return AccountRoutingFlatten.standard().readExternal(uuid);
                    } catch (Exception e) {
                        log.warn(e.getMessage(), e);
                        return null;
                    }
                }).filter(Objects::nonNull)
                .toList();
    }

    private static CreateAccountFreeSpinBatchRequest.Builder toCreateAccountFreeSpinBatchRequest(
            AddPlayersRequest request, String provider, List<AccountRoutingFlatten> flattens) {
        return CreateAccountFreeSpinBatchRequest.newBuilder()
                .setRequestId(request.requestId)
                .addAllPlayerIdList(toAccountIdLong(flattens))
                .setBrandName(request.brandName)
                .setBonusCode(request.bonusCode)
                .setProvider(provider)
                .setCurrency(request.currency);
    }

    private static CancelAccountFreeSpinRequest.Builder toCancelAccountFreeSpinRequest(
            RemovePlayersRequest request, String provider, List<AccountRoutingFlatten> flattens) {
        return CancelAccountFreeSpinRequest.newBuilder()
                .setBonusCode(request.removalBonus)
                .setProvider(provider)
                .setCurrency(Objects.requireNonNull(request.currency))
                .setBrandName(Objects.requireNonNull(request.brandName))
                .setRequestId(Objects.requireNonNull(request.requestId))
                .addAllPlayerIdList(toAccountIdLong(flattens));
    }

    private static CreateFreeSpinCampaignRequest.Builder toCreateFreeSpinCampaignRequest(
            CreateFreeRoundRequest createFreeRoundRequest, String provider) {
        var req = CreateFreeSpinCampaignRequest.newBuilder()
                .setProvider(provider)
                .setCurrency(createFreeRoundRequest.currency)
                .setBonusCode(createFreeRoundRequest.bonusCode)
                .setSpins(createFreeRoundRequest.rounds)
                .setBetValue(createFreeRoundRequest.betAmount.toString())
                .setBetType(StringUtils.isBlank(createFreeRoundRequest.betType) ? BET_PER_LINE : createFreeRoundRequest.betType)
                .addAllGameIds(getGameIds(createFreeRoundRequest.gameList))
                .setBrandName(createFreeRoundRequest.brandName);
        Optional.ofNullable(createFreeRoundRequest.campaignType).ifPresent(req::setType);
        Optional.ofNullable(createFreeRoundRequest.betLevel).ifPresent(req::setBetLevel);
        Optional.ofNullable(createFreeRoundRequest.startDate).map(BLOOMREACH_DATE_FORMAT::parse).map(FREE_SPIN_DATE_FORMATTER::format)
                .ifPresent(req::setStartDate);
        Optional.ofNullable(createFreeRoundRequest.endDate).map(BLOOMREACH_DATE_FORMAT::parse).map(FREE_SPIN_DATE_FORMATTER::format).ifPresent(req::setEndDate);
        return req;
    }

    static void checkAuthToken(String authHeader, String token) {
        boolean isValidAuth = StringUtils.isNotEmpty(authHeader) && authHeader.startsWith(BEARER_AUTH);
        var auth = isValidAuth ? authHeader.split(StringUtils.SPACE)[1] : StringUtils.EMPTY;
        AbstractApiEndpoint.malformed(auth, token);
    }

    private static List<String> getGameIds(List<bi.bloomreach.data.GameList> gameList) {
        return gameList.stream()
                .map(game -> game.gameId)
                .toList();
    }

    private static List<String> getValidUUIDs(RandomRewardRequest req) {
        return req.uuids
                .stream()
                .peek(uuid -> {
                    if (StringUtils.isEmpty(uuid)) {
                        log.warn("Empty UUID found in random reward request, origin requestId: {}", req.requestId);
                    }
                    if (!AccountRoutingFlatten.standard().isRouting(uuid)) {
                        log.warn("Invalid UUID found in random reward request, uuid: {}", uuid);
                    }
                })
                .filter(uuid -> StringUtils.isNotEmpty(uuid) && AccountRoutingFlatten.standard().isRouting(uuid))
                .collect(Collectors.toList());
    }

    private void createRandomRewardsInBackground(List<String> reqUUIDList, RandomRewardRequest req, AsyncResponse async) {
        CompletableFuture<?>[] futures = new CompletableFuture<?>[reqUUIDList.size()];
        for (int i = 0; i < reqUUIDList.size(); i++) {
            String reqUuid = reqUUIDList.get(i);
            futures[i] = randomRewardAccount(reqUuid, req, i).toCompletableFuture();
        }
        CompletableFuture.allOf(futures).whenComplete((BiConsumer<Object, Throwable>) (ignored, err) -> {
            if (Objects.nonNull(err)) {
                log.error("failed to random reward batch", err);
                async.resume(err);
            } else {
                async.resume(Response.accepted().build());
            }
        });
    }

    private static void handleTemplateLimits(GetRandomRewardTemplateLimitsResponse templateLimitsResponse, List<String> reqUUIDList) {
        if (!isTemplateLimited(templateLimitsResponse)) {
            return;
        }

        var availableInstanceAmount = getAvailableInstanceAmount(templateLimitsResponse);
        if (availableInstanceAmount == null) {
            return;
        }

        var reqUUIDListIterator = reqUUIDList.listIterator(reqUUIDList.size());
        var toBeDeleted = reqUUIDList.size() - availableInstanceAmount;
        while (reqUUIDListIterator.hasPrevious() && toBeDeleted > 0) {
            reqUUIDListIterator.previous();
            reqUUIDListIterator.remove();
            toBeDeleted--;
        }
    }

    private static Long getAvailableInstanceAmount(GetRandomRewardTemplateLimitsResponse templateLimitsResponse) {
        Long totalAvailableAmount = null;
        if (templateLimitsResponse.getLimitTotal() > 0) {
            totalAvailableAmount = templateLimitsResponse.getLimitTotal() - templateLimitsResponse.getIssuedTotal();
        }
        Long dailyAvailableAmount = null;
        if (templateLimitsResponse.getLimitPerDay() > 0) {
            dailyAvailableAmount = templateLimitsResponse.getLimitPerDay() - templateLimitsResponse.getIssuedPerDay();
        }

        if (totalAvailableAmount != null && dailyAvailableAmount != null) {
            return Math.min(totalAvailableAmount, dailyAvailableAmount);
        } else if (totalAvailableAmount != null) {
            return totalAvailableAmount;
        } else {
            return dailyAvailableAmount;
        }

    }

    private static boolean isTemplateLimited(GetRandomRewardTemplateLimitsResponse templateLimitsResponse) {
        return templateLimitsResponse.getLimitTotal() > 0 || templateLimitsResponse.getLimitPerDay() > 0;
    }

    private static SegmentFetchResponse fromProto(List<uam.api.v1.AccountTags> protoTags) {
        Map<String, Set<String>> accountTagsMap = new HashMap<>();

        for (uam.api.v1.AccountTags tag : protoTags) {
            accountTagsMap.put(
                    tag.getAccount(),
                    new HashSet<>(tag.getTagsList()));
        }

        return new SegmentFetchResponse(accountTagsMap);
    }

    private static long getRequestTimestamp(MultivaluedMap<String, String> requestHeaders) {
        if (requestHeaders.containsKey(HttpProto.HEADER_X_TIMESTAMP)) {
            return PlatformUtil.parseTimestamp(requestHeaders.getFirst(HttpProto.HEADER_X_TIMESTAMP)).toEpochMilli();
        }

        return System.currentTimeMillis();
    }
}
