package bi.bloomreach;

import com.turbospaces.annotations.ApiEndpoint;

import bi.bloomreach.data.AddPlayersRequest;
import bi.bloomreach.data.BloomreachAwardJackpotAccountFreeContributionRequest;
import bi.bloomreach.data.CancelFreeRoundRequest;
import bi.bloomreach.data.CreateFreeRoundRequest;
import bi.bloomreach.data.CreatePromotionRequest;
import bi.bloomreach.data.PreferencesRequest;
import bi.bloomreach.data.PromotionsToAccountsRequest;
import bi.bloomreach.data.RandomRewardInstanceCompleteRequest;
import bi.bloomreach.data.RandomRewardInstancesRequest;
import bi.bloomreach.data.RandomRewardRequest;
import bi.bloomreach.data.RemovePlayersRequest;
import bi.bloomreach.data.RewardRequest;
import bi.bloomreach.data.SegmentFetchRequest;
import bi.bloomreach.data.SegmentRequest;
import bi.bloomreach.data.UpdatePromotionRequest;
import bi.trustpilot.data.TrustpilotEmailInvitationRequest;
import com.turbospaces.http.HttpProto;
import io.netty.channel.ChannelHandlerContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Account management", description = "Account management API")
@SecurityScheme(type = SecuritySchemeType.APIKEY, name = "AccountManagementSecurity", in = SecuritySchemeIn.HEADER,
        description = "Enter the token with the `Bearer: ` prefix", paramName = HttpHeaders.AUTHORIZATION)
@SecurityRequirement(name = "AccountManagementSecurity")
@ApiResponses({
        @ApiResponse(responseCode = "202", description = "Success"),
        @ApiResponse(responseCode = "304", description = "Missing request id or uuids"),
        @ApiResponse(responseCode = "500", description = "Internal error") })
public interface AccountManagementEndpoint {

    @POST
    @Path("/reward")
    @ApiEndpoint(rateLimiterKey = "bloomreach.reward")
    @Operation(
            summary = "Account reward",
            description = "Add specified amount of GC/SC to account balance")
    void reward(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid RewardRequest rewardRequest) throws Exception;

    @POST
    @Path("/segment")
    @ApiEndpoint(rateLimiterKey = "bloomreach.segment")
    @Operation(
            summary = "Account segmentation",
            description = "Allows to assign or remove tags for account",
            parameters = {
                    @Parameter(name = HttpProto.HEADER_X_TIMESTAMP, in = ParameterIn.HEADER)
            })
    void segment(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid SegmentRequest rewardRequest) throws Exception;

    @POST
    @Path("/segment/fetch")
    @ApiEndpoint(rateLimiterKey = "bloomreach.segment.fetch")
    @Operation(
            summary = "Fetch tags for accounts",
            description = "Allows to read categories/tags from account")
    @ApiResponses({
            @ApiResponse(responseCode = "202", description = "Success"),
            @ApiResponse(responseCode = "500", description = "Internal error") })
    void fetchSegment(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid SegmentFetchRequest request) throws Exception;

    @POST
    @Path("/preferences")
    @ApiEndpoint(rateLimiterKey = "bloomreach.preferences")
    @Operation(
            summary = "Set account preferences",
            description = "Set account preferences for specified account")
    void preferences(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid PreferencesRequest preferencesRequest);

    @POST
    @Path("/{provider}/free-rounds")
    @ApiEndpoint(rateLimiterKey = "bloomreach.createFreeRound")
    @Operation(
            summary = "Create free spins",
            description = "Create free spins for specified games")
    void createFreeRound(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("provider") String provider,
            @NotNull @Valid CreateFreeRoundRequest createFreeRoundRequest) throws Exception;

    @DELETE
    @Path("/{provider}/free-rounds")
    @ApiEndpoint(rateLimiterKey = "bloomreach.cancelFreeRound")
    @Operation(
            summary = "Cancel free spins",
            description = "Cancel free spins for specified games")
    void cancelFreeRound(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("provider") String provider,
            @NotNull @Valid CancelFreeRoundRequest cancelFreeRoundRequest) throws Exception;

    @POST
    @Path("/{provider}/players")
    @ApiEndpoint(rateLimiterKey = "bloomreach.addPlayers")
    @Operation(
            summary = "Add free spins for players",
            description = "Add free spins for specified players")
    void addPlayers(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("provider") String provider,
            @NotNull @Valid AddPlayersRequest addPlayersRequest) throws Exception;

    @DELETE
    @Path("/{provider}/players")
    @ApiEndpoint(rateLimiterKey = "bloomreach.removePlayers")
    @Operation(
            summary = "Cancel free spins",
            description = "Cancel free spins for specified players")
    void removePlayers(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("provider") String provider,
            @NotNull @Valid RemovePlayersRequest removePlayersRequest) throws Exception;

    @POST
    @Path("/rating/invite")
    @Operation(hidden = true)
    @ApiEndpoint(rateLimiterKey = "bloomreach.ratingInvite")
    void ratingEmailInvite(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid TrustpilotEmailInvitationRequest request);

    @POST
    @Path("/account/award-free-contribution")
    @Operation(hidden = true)
    @ApiEndpoint(rateLimiterKey = "bloomreach.award-free-contribution")
    void awardJackpotAccountFreeContribution(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid BloomreachAwardJackpotAccountFreeContributionRequest rewardRequest) throws Exception;

    @POST
    @Path("/promotion")
    @ApiEndpoint(rateLimiterKey = "bloomreach.createPromotion")
    @Operation(
            summary = "Create promotions",
            description = "Create promotions")
    void createPromotion(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid CreatePromotionRequest createPromotionRequest) throws Exception;

    @PUT
    @Path("/promotion")
    @ApiEndpoint(rateLimiterKey = "bloomreach.updatePromotion")
    @Operation(
            summary = "Update promotion by code and brand name",
            description = "Update promotion - one of the fields: active flag, url, title, description, start date, expire date")
    void updatePromotion(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid UpdatePromotionRequest updatePromotionRequest) throws Exception;

    @POST
    @Path("/promotions")
    @ApiEndpoint(rateLimiterKey = "bloomreach.assignPromotions")
    @Operation(
            summary = "Assign promotions",
            description = "Assign promotions to accounts")
    void assignPromotions(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid PromotionsToAccountsRequest request);

    @DELETE
    @Path("/promotions")
    @ApiEndpoint(rateLimiterKey = "bloomreach.unassignPromotions")
    @Operation(
            summary = "Unassign promotions",
            description = "Remove promotions from accounts")
    void unassignPromotions(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid PromotionsToAccountsRequest request);

    @POST
    @Path("/random-reward")
    @ApiEndpoint(rateLimiterKey = "bloomreach.random-reward")
    @Operation(
            summary = "Random-reward",
            description = "Assign random rewards")
    void createRandomRewardInstance(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid RandomRewardRequest rewardRequest) throws Exception;

    @POST
    @Path("/random-reward/list")
    @ApiEndpoint(rateLimiterKey = "bloomreach.random-reward.list")
    @Operation(
            summary = "Random-reward-instances",
            description = "Get random reward instances")
    void getRandomRewardInstances(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid RandomRewardInstancesRequest rewardRequest) throws Exception;

    @POST
    @Path("/random-reward/list/count")
    @ApiEndpoint(rateLimiterKey = "bloomreach.random-reward.list")
    @Operation(
            summary = "Random-reward-instances",
            description = "Get random reward instances")
    void getRandomRewardInstancesCount(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid RandomRewardInstancesRequest rewardRequest) throws Exception;

    @POST
    @Path("/random-reward/complete")
    @ApiEndpoint(rateLimiterKey = "bloomreach.random-reward.complete")
    @Operation(
            summary = "Random-reward-instance-complete",
            description = "Complete random reward instance")
    void completeRandomRewardInstances(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @NotNull @Valid RandomRewardInstanceCompleteRequest rewardRequest) throws Exception;
}
