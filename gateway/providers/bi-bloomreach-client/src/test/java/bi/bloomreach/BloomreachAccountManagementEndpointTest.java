package bi.bloomreach;

import static bi.bloomreach.AbstractAccountManagementEndpoint.BEARER_AUTH;
import static bi.bloomreach.BloomreachUserManagementEndpointTest.API_TOKEN;
import static bi.bloomreach.PromotionDataGenerator.BRAND_NAME;
import static bi.bloomreach.PromotionDataGenerator.TEST_CODE;
import static bi.bloomreach.PromotionDataGenerator.genAssignPromotionsRequests;
import static bi.bloomreach.PromotionDataGenerator.genCreatePromotionRequest;
import static bi.bloomreach.PromotionDataGenerator.genCreatePromotionRequestApi;
import static bi.bloomreach.PromotionDataGenerator.genNoAccountsRequest;
import static bi.bloomreach.PromotionDataGenerator.genNoPromotionsRequest;
import static bi.bloomreach.PromotionDataGenerator.genPromotionsToAccountsRequest;
import static bi.bloomreach.PromotionDataGenerator.genRoutingKeys;
import static bi.bloomreach.PromotionDataGenerator.genUnassignPromotionsRequests;
import static bi.bloomreach.PromotionDataGenerator.genUpdatePromotionRequest;
import static bi.bloomreach.PromotionDataGenerator.genUpdatePromotionRequestApi;
import static org.apache.commons.lang3.StringUtils.SPACE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import gamehub.GameHubServiceApi;
import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import io.netty.util.AsciiString;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.glassfish.grizzly.http.server.HttpServer;
import org.glassfish.jersey.internal.inject.AbstractBinder;
import org.glassfish.jersey.jackson.internal.jackson.jaxrs.json.JacksonJaxbJsonProvider;
import org.glassfish.jersey.server.ResourceConfig;
import org.glassfish.jersey.test.DeploymentContext;
import org.glassfish.jersey.test.JerseyTest;
import org.glassfish.jersey.test.grizzly.GrizzlyTestContainerFactory;
import org.glassfish.jersey.test.spi.TestContainer;
import org.glassfish.jersey.test.spi.TestContainerException;
import org.glassfish.jersey.test.spi.TestContainerFactory;
import org.jboss.resteasy.specimpl.MultivaluedTreeMap;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.ConfigurableCloudConnector;
import org.springframework.cloud.DynamicCloud;

import com.google.protobuf.InvalidProtocolBufferException;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import api.DefaultApiFactory;
import api.v1.ApiFactory;
import api.v1.PlatformSpec;
import bi.bloomreach.data.CreatePromotionRequest;
import bi.bloomreach.data.PromotionsToAccountsRequest;
import bi.bloomreach.data.UpdatePromotionRequest;
import bi.trustpilot.TrustpilotClient;
import gateway.CrmGatewayPropertiesFragement;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.netty.channel.ChannelHandlerContext;
import jakarta.ws.rs.client.Entity;
import jakarta.ws.rs.core.Application;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import payment.api.PaymentServiceApi;
import uam.api.UamServiceApi;
import uam.api.v1.AssignPromotionsRequest;
import uam.api.v1.UnassignPromotionsRequest;
import worker.WorkerServiceApi;
import worker.api.v1.RoutableRequest;

@ExtendWith(MockitoExtension.class)
class BloomreachAccountManagementEndpointTest extends JerseyTest {
    private static final CommonObjectMapper OBJECT_MAPPER = new CommonObjectMapper();
    private static final JacksonJaxbJsonProvider PROVIDER = new JacksonJaxbJsonProvider();

    static {
        PROVIDER.setMapper(OBJECT_MAPPER);
    }
    @Mock
    private WorkerServiceApi workerApi;

    @Mock
    private GameHubServiceApi gameHubApi;

    @Mock
    private PaymentServiceApi paymentServiceApi;

    @Mock
    private UamServiceApi uamServiceApi;

    @Mock
    private TrustpilotClient trustpilotClient;

    @Captor
    private ArgumentCaptor<RoutableRequest> requestArgumentCaptor;

    @Mock
    private ChannelHandlerContext ctx;

    private BloomreachAccountManagementEndpoint underTest;

    @Override
    @BeforeEach
    public void setUp() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        CrmGatewayPropertiesFragement fragement = new CrmGatewayPropertiesFragement(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        String uri = "https://user:<EMAIL>?projectToken=d77a4852-2720-433d-b70b-b6aad343013c&altUUID=true&apiToken=" + API_TOKEN;
        cloud.addUps(new PlainServiceInfo("bluedream-bloomreach", uri));

        underTest = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory, cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        underTest.setBeanName(PlatformUtil.randomUUID().toString());
        underTest.afterPropertiesSet();

        super.setUp();
    }
    @Override
    @AfterEach
    public void tearDown() throws Exception {
        super.tearDown();
        underTest.destroy();
    }

    @Test
    void shouldCreatePromotionSuccessfully() throws InvalidProtocolBufferException {

        CompletableFuture<?> completableFutureOk = CompletableFuture.completedFuture("OK");
        doReturn(completableFutureOk).when(workerApi).processInBackground(any(RoutableRequest.class), any(AsciiString.class));

        Entity<CreatePromotionRequest> entity = Entity.entity(genCreatePromotionRequestApi(), MediaType.APPLICATION_JSON_TYPE);
        try (Response response = target("/v1/bloomreach/promotion").register(PROVIDER).request(MediaType.APPLICATION_JSON)
                .headers(buildHeaders())
                .post(entity)) {
            response.bufferEntity();
            assertThat(response.getStatus()).isEqualTo(Response.Status.ACCEPTED.getStatusCode());
        }

        verify(workerApi).processInBackground(requestArgumentCaptor.capture(), any(AsciiString.class));
        assertThat(requestArgumentCaptor.getValue().getRequest().unpack(uam.api.v1.CreatePromotionRequest.class))
                .isEqualTo(genCreatePromotionRequest());
        assertThat(requestArgumentCaptor.getValue().getRoutingKey()).isEqualTo(TEST_CODE + "-" + BRAND_NAME);

    }

    @Test
    void shouldUpdatePromotionSuccessfully() throws InvalidProtocolBufferException {
        CompletableFuture<?> completableFutureOk = CompletableFuture.completedFuture("OK");
        doReturn(completableFutureOk).when(workerApi).processInBackground(any(RoutableRequest.class), any(AsciiString.class));

        Entity<UpdatePromotionRequest> entity = Entity.entity(genUpdatePromotionRequestApi(), MediaType.APPLICATION_JSON_TYPE);
        try (Response response = target("/v1/bloomreach/promotion").register(PROVIDER).request(MediaType.APPLICATION_JSON)
                .headers(buildHeaders())
                .put(entity)) {
            response.bufferEntity();
            assertThat(response.getStatus()).isEqualTo(Response.Status.ACCEPTED.getStatusCode());
        }

        verify(workerApi).processInBackground(requestArgumentCaptor.capture(), any(AsciiString.class));
        assertThat(requestArgumentCaptor.getValue().getRequest().unpack(uam.api.v1.UpdatePromotionRequest.class))
                .isEqualTo(genUpdatePromotionRequest());
        assertThat(requestArgumentCaptor.getValue().getRoutingKey()).isEqualTo(TEST_CODE + "-" + BRAND_NAME);
    }

    @Test
    void shouldAssignPromotionsSuccessfully() {
        CompletableFuture<?> completableFutureOk = CompletableFuture.completedFuture("OK");
        doReturn(completableFutureOk).when(workerApi).processInBackground(any(RoutableRequest.class), any(AsciiString.class));

        Entity<PromotionsToAccountsRequest> entity = Entity.entity(genPromotionsToAccountsRequest(), MediaType.APPLICATION_JSON_TYPE);
        try (Response response = target("/v1/bloomreach/promotions").register(PROVIDER).request(MediaType.APPLICATION_JSON)
                .headers(buildHeaders())
                .post(entity)) {
            response.bufferEntity();
            assertThat(response.getStatus()).isEqualTo(Response.Status.ACCEPTED.getStatusCode());
        }

        verify(workerApi, times(2)).processInBackground(requestArgumentCaptor.capture(), any(AsciiString.class));
        assertThat(requestArgumentCaptor.getAllValues().stream().map(captor -> {
            try {
                return captor.getRequest().unpack(AssignPromotionsRequest.class);
            } catch (InvalidProtocolBufferException e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toSet()))
                .containsExactlyInAnyOrderElementsOf(genAssignPromotionsRequests());

        assertThat(requestArgumentCaptor.getAllValues().stream().map(RoutableRequest::getRoutingKey).collect(Collectors.toSet()))
                .isEqualTo(genRoutingKeys());
    }

    @Test
    void shouldNotAssignWhenNoPromotions() {

        Entity<PromotionsToAccountsRequest> entity = Entity.entity(genNoPromotionsRequest(), MediaType.APPLICATION_JSON_TYPE);
        try (Response response = target("/v1/bloomreach/promotions").register(PROVIDER).request(MediaType.APPLICATION_JSON)
                .headers(buildHeaders())
                .post(entity)) {
            response.bufferEntity();
            assertThat(response.getStatus()).isEqualTo(Response.Status.NOT_MODIFIED.getStatusCode());
        }

        verifyNoInteractions(workerApi);
    }

    @Test
    void shouldNotAssignPromotionsWhenNoAccounts() {

        Entity<PromotionsToAccountsRequest> entity = Entity.entity(genNoAccountsRequest(), MediaType.APPLICATION_JSON_TYPE);
        try (Response response = target("/v1/bloomreach/promotions").register(PROVIDER).request(MediaType.APPLICATION_JSON)
                .headers(buildHeaders())
                .post(entity)) {
            response.bufferEntity();
            assertThat(response.getStatus()).isEqualTo(Response.Status.NOT_MODIFIED.getStatusCode());
        }

        verifyNoInteractions(workerApi);
    }

    @Test
    void shouldUnassignPromotionsSuccessfully() {
        CompletableFuture<?> completableFutureOk = CompletableFuture.completedFuture("OK");
        doReturn(completableFutureOk).when(workerApi).processInBackground(any(RoutableRequest.class), any(AsciiString.class));

        Entity<PromotionsToAccountsRequest> entity = Entity.entity(genPromotionsToAccountsRequest(), MediaType.APPLICATION_JSON_TYPE);
        try (Response response = target("/v1/bloomreach/promotions")
                .register(PROVIDER).request(MediaType.APPLICATION_JSON)
                .headers(buildHeaders())
                .build("DELETE", entity)
                .invoke()) {
            response.bufferEntity();
            assertThat(response.getStatus()).isEqualTo(Response.Status.ACCEPTED.getStatusCode());
        }

        verify(workerApi, times(2)).processInBackground(requestArgumentCaptor.capture(), any(AsciiString.class));
        assertThat(requestArgumentCaptor.getAllValues().stream().map(captor -> {
            try {
                return captor.getRequest().unpack(UnassignPromotionsRequest.class);
            } catch (InvalidProtocolBufferException e) {
                throw new RuntimeException(e);
            }
        }).collect(Collectors.toSet()))
                .containsExactlyInAnyOrderElementsOf(genUnassignPromotionsRequests());

        assertThat(requestArgumentCaptor.getAllValues().stream().map(RoutableRequest::getRoutingKey).collect(Collectors.toSet()))
                .isEqualTo(genRoutingKeys());
    }

    @Override
    protected Application configure() {
        return new ResourceConfig(BloomreachAccountManagementEndpoint.class)
                .register(new AbstractBinder() {
                    @Override
                    protected void configure() {
                        bind(workerApi).to(WorkerServiceApi.class);
                        bind(underTest).to(BloomreachAccountManagementEndpoint.class);
                        bind(ctx).to(ChannelHandlerContext.class);
                    }
                }).register(PROVIDER);
    }

    @Override
    protected TestContainerFactory getTestContainerFactory() throws TestContainerException {
        return new TestContainerFactory() {

            private final GrizzlyTestContainerFactory grizzlyTestContainerFactory = new GrizzlyTestContainerFactory();

            @Override
            public TestContainer create(URI baseUri, DeploymentContext deploymentContext) {
                TestContainer testContainer = grizzlyTestContainerFactory.create(baseUri, deploymentContext);
                try {
                    HttpServer server = (HttpServer) FieldUtils.readDeclaredField(testContainer, "server", true);
                    server.getServerConfiguration().setAllowPayloadForUndefinedHttpMethods(true);
                } catch (IllegalAccessException e) {
                    fail(e.getMessage());
                }
                return testContainer;
            }
        };
    }

    private static MultivaluedMap<String, Object> buildHeaders() {
        MultivaluedMap<String, Object> requestHeaders = new MultivaluedTreeMap<>();
        requestHeaders.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED);
        requestHeaders.add(HttpProto.HEADER_X_PLATFORM, PlatformSpec.WEB.code());
        requestHeaders.add(HttpHeaders.AUTHORIZATION, String.join(SPACE, BEARER_AUTH, API_TOKEN));
        return requestHeaders;
    }
}
