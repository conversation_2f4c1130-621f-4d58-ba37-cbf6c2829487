
package bi.bloomreach;

import static bi.bloomreach.AbstractAccountManagementEndpoint.BEARER_AUTH;
import static org.apache.commons.lang3.StringUtils.SPACE;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;

import java.util.List;
import java.util.Set;

import org.jboss.resteasy.specimpl.MultivaluedTreeMap;
import org.jboss.resteasy.specimpl.ResteasyHttpHeaders;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.ConfigurableCloudConnector;
import org.springframework.cloud.DynamicCloud;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.HttpProto;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import api.DefaultApiFactory;
import api.v1.ApiFactory;
import api.v1.PlatformSpec;
import bi.bloomreach.data.AddPlayersRequest;
import bi.bloomreach.data.CancelFreeRoundRequest;
import bi.bloomreach.data.RemovePlayersRequest;
import bi.bloomreach.data.SegmentRequest;
import bi.trustpilot.TrustpilotClient;
import gamehub.GameHubServiceApi;
import gateway.CrmGatewayPropertiesFragement;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import io.netty.channel.ChannelHandlerContext;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.MultivaluedMap;
import payment.api.PaymentServiceApi;
import uam.api.UamServiceApi;
import worker.WorkerServiceApi;

@ExtendWith(MockitoExtension.class)
class BloomreachUserManagementEndpointTest {

    public static final String API_TOKEN = "fd753645-a093-4af5-bf15-bf136e741ae1";

    @Mock
    private WorkerServiceApi workerApi;

    @Mock
    private GameHubServiceApi gameHubApi;

    @Mock
    private PaymentServiceApi paymentServiceApi;

    @Mock
    private UamServiceApi uamServiceApi;

    @Mock
    private TrustpilotClient trustpilotClient;

    private CrmGatewayPropertiesFragement fragement;

    @Mock
    private ChannelHandlerContext ctx;

    @Test
    void cancelFreeRoundPriorityQueueTest() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        initProperties(cfg, cloud);

        BloomreachAccountManagementEndpoint endpoint = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory,
                cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        endpoint.setBeanName(PlatformUtil.randomUUID().toString());
        endpoint.afterPropertiesSet();

        AsyncResponse async = Mockito.mock(AsyncResponse.class);
        CancelFreeRoundRequest bloomreachCancelFreeRoundRequest = generateCancelRequest();

        endpoint.cancelFreeRound(async, ctx, buildHeaders(), "evoplay", bloomreachCancelFreeRoundRequest);

        Mockito.verify(gameHubApi, Mockito.timeout(1000)).processInBackground(any(api.v1.RoutableRequest.class), any());
    }

    @Test
    void cancelFreeRoundTest() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        initProperties(cfg, cloud);

        BloomreachAccountManagementEndpoint endpoint = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory,
                cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        endpoint.setBeanName(PlatformUtil.randomUUID().toString());
        endpoint.afterPropertiesSet();

        AsyncResponse async = Mockito.mock(AsyncResponse.class);
        CancelFreeRoundRequest bloomreachCancelFreeRoundRequest = generateCancelRequest();

        endpoint.cancelFreeRound(async, ctx, buildHeaders(), "evoplay", bloomreachCancelFreeRoundRequest);

        Mockito.verify(gameHubApi, Mockito.timeout(1000)).processInBackground(any(api.v1.RoutableRequest.class), any());
    }

    @Test
    void addPlayersPriorityQueueTest() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        initProperties(cfg, cloud);

        BloomreachAccountManagementEndpoint endpoint = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory,
                cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        endpoint.setBeanName(PlatformUtil.randomUUID().toString());
        endpoint.afterPropertiesSet();

        AsyncResponse async = Mockito.mock(AsyncResponse.class);
        AddPlayersRequest addPlayersRequest = generateAddPlayerRequest();

        endpoint.addPlayers(async, ctx, buildHeaders(), "evoplay", addPlayersRequest);

        Mockito.verify(gameHubApi, Mockito.timeout(1000).times(1)).processInBackground(any(api.v1.RoutableRequest.class), any());
    }

    @Test
    void addPlayersTest() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        initProperties(cfg, cloud);

        BloomreachAccountManagementEndpoint endpoint = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory,
                cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        endpoint.setBeanName(PlatformUtil.randomUUID().toString());
        endpoint.afterPropertiesSet();

        AsyncResponse async = Mockito.mock(AsyncResponse.class);
        AddPlayersRequest addPlayersRequest = generateAddPlayerRequest();

        endpoint.addPlayers(async, ctx, buildHeaders(), "evoplay", addPlayersRequest);

        Mockito.verify(gameHubApi, Mockito.timeout(1000).times(1)).processInBackground(any(api.v1.RoutableRequest.class), any());
    }

    @Test
    void addPragmaticPlayersTest() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        initProperties(cfg, cloud);

        BloomreachAccountManagementEndpoint endpoint = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory,
                cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        endpoint.setBeanName(PlatformUtil.randomUUID().toString());
        endpoint.afterPropertiesSet();

        AsyncResponse async = Mockito.mock(AsyncResponse.class);
        AddPlayersRequest addPlayersRequest = generateAddPlayerRequest();

        endpoint.addPlayers(async, ctx, buildHeaders(), "pragmatic", addPlayersRequest);

        Mockito.verify(gameHubApi, Mockito.timeout(1000).times(1)).processInBackground(any(api.v1.RoutableRequest.class), any());
    }

    @Test
    void testAddMultiplePlayersWithoutBatchPriorityQueue() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        initProperties(cfg, cloud);

        BloomreachAccountManagementEndpoint endpoint = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory,
                cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        endpoint.setBeanName(PlatformUtil.randomUUID().toString());
        endpoint.afterPropertiesSet();

        AsyncResponse async = Mockito.mock(AsyncResponse.class);
        AddPlayersRequest addPlayersRequest = generateAddPlayerRequest();

        endpoint.addPlayers(async, ctx, buildHeaders(), "infingame", addPlayersRequest);

        Mockito.verify(gameHubApi, Mockito.timeout(1000).times(1)).processInBackground(any(api.v1.RoutableRequest.class), any());
    }

    @Test
    void testAddMultiplePlayersWithoutBatch() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        initProperties(cfg, cloud);

        BloomreachAccountManagementEndpoint endpoint = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory,
                cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        endpoint.setBeanName(PlatformUtil.randomUUID().toString());
        endpoint.afterPropertiesSet();

        AsyncResponse async = Mockito.mock(AsyncResponse.class);
        AddPlayersRequest addPlayersRequest = generateAddPlayerRequest();

        endpoint.addPlayers(async, ctx, buildHeaders(), "infingame", addPlayersRequest);

        Mockito.verify(gameHubApi, Mockito.timeout(1000).times(1)).processInBackground(any(api.v1.RoutableRequest.class), any());
    }

    @Test
    void removePlayersPriorityQueueTest() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        initProperties(cfg, cloud);

        BloomreachAccountManagementEndpoint endpoint = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory,
                cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        endpoint.setBeanName(PlatformUtil.randomUUID().toString());
        endpoint.afterPropertiesSet();

        AsyncResponse async = Mockito.mock(AsyncResponse.class);
        RemovePlayersRequest removePlayersRequest = generateRemovePlayerRequest();

        endpoint.removePlayers(async, ctx, buildHeaders(), "evoplay", removePlayersRequest);

        Mockito.verify(gameHubApi, Mockito.timeout(1000).times(1)).processInBackground(any(api.v1.RoutableRequest.class), any());
    }

    @Test
    void removePlayersTest() throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        ApplicationProperties props = new ApplicationProperties(cfg.factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        ApiFactory apiFactory = new DefaultApiFactory(props, new CommonObjectMapper());

        initProperties(cfg, cloud);

        BloomreachAccountManagementEndpoint endpoint = new BloomreachAccountManagementEndpoint(
                props,
                apiFactory,
                cloud,
                new SimpleMeterRegistry(),
                workerApi,
                gameHubApi,
                paymentServiceApi,
                uamServiceApi,
                trustpilotClient, fragement);
        endpoint.setBeanName(PlatformUtil.randomUUID().toString());
        endpoint.afterPropertiesSet();

        AsyncResponse async = Mockito.mock(AsyncResponse.class);
        RemovePlayersRequest removePlayersRequest = generateRemovePlayerRequest();

        endpoint.removePlayers(async, ctx, buildHeaders(), "evoplay", removePlayersRequest);

        Mockito.verify(gameHubApi, Mockito.timeout(1000).times(1)).processInBackground(any(api.v1.RoutableRequest.class), any());
    }

    @Test
    void validRequest() {
        SegmentRequest request = new SegmentRequest();
        request.brand = "bluedream";
        request.addTags = "tag1,tag2";
        request.requestId = "any";
        request.uuids = Set.of("qwertyui/1234", "qwertyui/1235");
        assertTrue(AbstractAccountManagementEndpoint.validRequest(request));
        request.uuids = Set.of("qwertyui/1234", "qwertyui/12aa35");
        assertFalse(AbstractAccountManagementEndpoint.validRequest(request));
    }


    @Test
    void validUUID() {
        assertTrue(AbstractAccountManagementEndpoint.validUUID("qwertyui/1234"));
        assertTrue(AbstractAccountManagementEndpoint.validUUID("aqwertyui/1234"));
        assertFalse(AbstractAccountManagementEndpoint.validUUID("aqwertyu/1234'"));
        assertFalse(AbstractAccountManagementEndpoint.validUUID("qwe,,tyu/1234"));
        assertTrue(AbstractAccountManagementEndpoint.validUUID("qwetyu/1234"));
        assertFalse(AbstractAccountManagementEndpoint.validUUID(null));
        assertFalse(AbstractAccountManagementEndpoint.validUUID(""));
        assertFalse(AbstractAccountManagementEndpoint.validUUID(" "));
        assertFalse(AbstractAccountManagementEndpoint.validUUID("whatever"));
    }

    private void initProperties(ApplicationConfig cfg, DynamicCloud cloud) {
        fragement = new CrmGatewayPropertiesFragement(cfg.factory());
        String uri = "https://user:<EMAIL>?projectToken=d77a4852-2720-433d-b70b-b6aad343013c&altUUID=true&apiToken=" + API_TOKEN;

        cloud.addUps(new PlainServiceInfo("bluedream-bloomreach", uri));
    }

    private static CancelFreeRoundRequest generateCancelRequest() {
        CancelFreeRoundRequest bloomreachCancelFreeRoundRequest = new CancelFreeRoundRequest();
        bloomreachCancelFreeRoundRequest.brandName = "bluedream";
        bloomreachCancelFreeRoundRequest.bonusCode = "promo0003";
        bloomreachCancelFreeRoundRequest.currency = "GC";

        return bloomreachCancelFreeRoundRequest;
    }

    private static AddPlayersRequest generateAddPlayerRequest() {
        AddPlayersRequest request = new AddPlayersRequest();
        request.bonusCode = "promo0003";
        request.uuids = List.of("666666/1", "1111/2");
        request.currency = "GC";
        request.brandName = "bluedream";
        request.requestId = PlatformUtil.randomUUID().toString();

        return request;
    }

    private static RemovePlayersRequest generateRemovePlayerRequest() {
        RemovePlayersRequest request = new RemovePlayersRequest();
        request.removalBonus = "promo0003";
        request.uuids = List.of("666666/1", "1111/2");
        request.currency = "GC";
        request.brandName = "bluedream";
        request.requestId = PlatformUtil.randomUUID().toString();

        return request;
    }

    private static HttpHeaders buildHeaders() {
        MultivaluedMap<String, String> requestHeaders = new MultivaluedTreeMap<>();
        requestHeaders.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED);
        requestHeaders.add(HttpProto.HEADER_X_PLATFORM, PlatformSpec.WEB.code());
        requestHeaders.add(HttpHeaders.AUTHORIZATION, String.join(SPACE, BEARER_AUTH, API_TOKEN));
        return new ResteasyHttpHeaders(requestHeaders);
    }
}
