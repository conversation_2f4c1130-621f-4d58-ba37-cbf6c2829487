package bi.bloomreach;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Set;

import api.v1.PlatformSpec;
import bi.bloomreach.data.CreatePromotionRequest;
import bi.bloomreach.data.PromotionsToAccountsRequest;
import bi.bloomreach.data.UpdatePromotionRequest;
import uam.api.v1.AssignPromotionsRequest;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;
import uam.api.v1.UnassignPromotionsRequest;

public class PromotionDataGenerator {
    public static final String TEST_CODE = "promo code";

    public static final String BRAND_NAME = "bluedream";

    public static CreatePromotionRequest genCreatePromotionRequestApi() {
        CreatePromotionRequest promotionRequest = new CreatePromotionRequest();
        promotionRequest.title = "Title " + TEST_CODE;
        promotionRequest.code = TEST_CODE;
        promotionRequest.brandName = BRAND_NAME;
        promotionRequest.startDate = LocalDateTime.ofInstant(Instant.parse("2023-10-30T18:35:00.00Z"), ZoneOffset.UTC);
        promotionRequest.url = "http://localhost/promo.html";
        promotionRequest.inactive = true;
        promotionRequest.expireDate = LocalDateTime.ofInstant(Instant.parse("2023-11-20T10:10:00.00Z"), ZoneOffset.UTC);
        promotionRequest.description = "Description " + TEST_CODE;
        return promotionRequest;
    }

    public static uam.api.v1.CreatePromotionRequest genCreatePromotionRequest() {
        return uam.api.v1.CreatePromotionRequest.newBuilder()
                .setCode(TEST_CODE)
                .setTitle("Title " + TEST_CODE)
                .setDescription("Description " + TEST_CODE)
                .setUrl("http://localhost/promo.html")
                .setBrandName(BRAND_NAME)
                .setInactive(true)
                .setStartAt(Instant.parse("2023-10-30T18:35:00.00Z").toEpochMilli())
                .setExpireAt(Instant.parse("2023-11-20T10:10:00.00Z").toEpochMilli())
                .build();
    }

    public static uam.api.v1.UpdatePromotionRequest genUpdatePromotionRequest() {
        return uam.api.v1.UpdatePromotionRequest.newBuilder()
                .setCode(TEST_CODE)
                .setBrandName(BRAND_NAME)
                .setUrl("http://localhost/updatedUrl.html")
                .setTitle("Updated title " + TEST_CODE)
                .setExpireAt(Instant.parse("2024-01-01T22:30:00.00Z").toEpochMilli())
                .setInactive("true")
                .build();
    }

    public static UpdatePromotionRequest genUpdatePromotionRequestApi() {
        UpdatePromotionRequest request = new UpdatePromotionRequest();
        request.brandName = BRAND_NAME;
        request.code = TEST_CODE;
        request.inactive = true;
        request.title = "Updated title " + TEST_CODE;
        request.url = "http://localhost/updatedUrl.html";
        request.expireDate = LocalDateTime.ofInstant(Instant.parse("2024-01-01T22:30:00.00Z"), ZoneOffset.UTC);
        return request;
    }

    public static PromotionsToAccountsRequest genPromotionsToAccountsRequest() {
        PromotionsToAccountsRequest request = new PromotionsToAccountsRequest();
        request.brandName = BRAND_NAME;
        request.uuids = Set.of("dd0cc6e3/1817775", "ed0befbf/713252");
        request.promotionCodes = Set.of(TEST_CODE);
        return request;
    }

    public static PromotionsToAccountsRequest genNoPromotionsRequest() {
        PromotionsToAccountsRequest request = new PromotionsToAccountsRequest();
        request.brandName = BRAND_NAME;
        request.uuids = Set.of("dd0cc6e3/1817775", "ed0befbf/713252");
        return request;
    }

    public static PromotionsToAccountsRequest genNoAccountsRequest() {
        PromotionsToAccountsRequest request = new PromotionsToAccountsRequest();
        request.brandName = BRAND_NAME;
        request.promotionCodes = Set.of(TEST_CODE);
        return request;
    }

    public static Set<AssignPromotionsRequest> genAssignPromotionsRequests() {
        return Set.of(genAssignPromotionsRequest(1817775),
                genAssignPromotionsRequest(713252));
    }

    public static AssignPromotionsRequest genAssignPromotionsRequest(long accountId) {
        return AssignPromotionsRequest.newBuilder()
                .addAllCodes(List.of(TEST_CODE))
                .setIdentity(buildIdentity(accountId))
                .build();
    }

    public static UnassignPromotionsRequest genUnassignPromotionsRequest(long accountId) {
        return UnassignPromotionsRequest.newBuilder()
                .addAllCodes(List.of(TEST_CODE))
                .setIdentity(buildIdentity(accountId))
                .build();
    }

    public static Set<UnassignPromotionsRequest> genUnassignPromotionsRequests() {
        return Set.of(genUnassignPromotionsRequest(1817775),
                genUnassignPromotionsRequest(713252));
    }

    public static Identity buildIdentity(Long accountId) {
        return Identity.newBuilder().setByAccountId(IdentityByAccountId.newBuilder()
                        .setAccountId(accountId)
                        .setPlatform(PlatformSpec.WEB.code()))
                .build();
    }

    public static Set<String> genRoutingKeys() {
        return Set.of("dd0cc6e3", "ed0befbf");
    }

}
