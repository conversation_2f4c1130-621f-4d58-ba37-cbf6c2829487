package bi.bloomreach.data;

import static bi.bloomreach.PromotionDataGenerator.TEST_CODE;
import static bi.bloomreach.PromotionDataGenerator.buildIdentity;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.math.BigDecimal;
import java.util.Set;

import org.junit.jupiter.api.Test;

import api.v1.ProviderSpec;
import bi.bloomreach.PromotionDataGenerator;
import gaming.patrianna.PatriannaFreeRound;

public class BloomreachMapperTest {

    @Test
    public void test_returns_patrianna_free_round_with_expected_values() {
        // Arrange
        CreateFreeRoundRequest createFreeRoundRequest = new CreateFreeRoundRequest();

        createFreeRoundRequest.brandName = "bluedream";
        String provider = ProviderSpec.EVOPLAY.code();
        createFreeRoundRequest.rounds = 10;
        createFreeRoundRequest.bonusCode = "bonus_code";
        createFreeRoundRequest.currency = "USD";
        createFreeRoundRequest.endDateAddOffer = "2022-12-31";
        createFreeRoundRequest.startDate = "2022-01-01";
        String gameId = "game_id";
        BigDecimal totalBet = new BigDecimal("100.00");

        // Act
        PatriannaFreeRound result = BloomreachMapper.toPatriannaFreeRound(createFreeRoundRequest, gameId, totalBet, provider);

        // Assert
        assertEquals(createFreeRoundRequest.campaignType, result.type);
        assertEquals(createFreeRoundRequest.brandName, result.brand);
        assertEquals(provider, result.provider);
        assertEquals(createFreeRoundRequest.rounds, result.spins);
        assertEquals(createFreeRoundRequest.bonusCode, result.campaign);
        assertEquals(gameId, result.games.getFirst());
        assertEquals(totalBet, result.betAmount);
        assertEquals(createFreeRoundRequest.currency, result.currency);
        assertEquals(createFreeRoundRequest.startDate, result.startDate);
        assertEquals(createFreeRoundRequest.endDateAddOffer, result.expirationDate);
    }

    @Test
    void shouldMapToCreatePromotionRequest() {
        var request = PromotionDataGenerator.genCreatePromotionRequestApi();
        var expected = PromotionDataGenerator.genCreatePromotionRequest();

        var actual = BloomreachMapper.toCreatePromotionRequest(request);

        assertThat(actual.build()).isEqualTo(expected);
    }

    @Test
    void shouldMapToUpdatePromotionRequest() {
        var request = PromotionDataGenerator.genUpdatePromotionRequestApi();
        var expected = PromotionDataGenerator.genUpdatePromotionRequest();

        var actual = BloomreachMapper.toUpdatePromotionRequest(request);

        assertThat(actual.build()).isEqualTo(expected);
    }

    @Test
    void shouldMapToUnassignPromotionsRequest() {
        var expected = PromotionDataGenerator.genUnassignPromotionsRequest(1L);

        var actual = BloomreachMapper.toUnassignPromotionsRequest(Set.of(TEST_CODE), buildIdentity(1L));

        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void shouldMapToAssignPromotionsRequest() {
        var expected = PromotionDataGenerator.genAssignPromotionsRequest(1L);

        var actual = BloomreachMapper.toAssignPromotionsRequest(Set.of(TEST_CODE), buildIdentity(1L));

        assertThat(actual).isEqualTo(expected);
    }
}
