package worker;

import ebean.OpenPGPEncryptor;
import java.util.LinkedList;

import javax.sql.DataSource;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EbeanDatabaseConfig;

import io.ebean.config.EncryptKeyManager;
import io.ebean.config.dbplatform.DatabasePlatform;
import io.ebean.platform.postgres.Postgres9Platform;

public abstract class WorkerEbeanConfiguration extends EbeanDatabaseConfig {
    public WorkerEbeanConfiguration(DataSource ds, ApplicationProperties props, EncryptKeyManager encryptKeyManager) {
        this(ds, props, encryptKeyManager, new Postgres9Platform(), new WorkerEntities());
    }
    public WorkerEbeanConfiguration(
            DataSource ds,
            ApplicationProperties props,
            EncryptKeyManager encryptKeyManager,
            DatabasePlatform databasePlatform,
            LinkedList<Class<?>> entities) {
        super(ds, props);
        setEncryptKeyManager(encryptKeyManager);
        setEncryptor(new OpenPGPEncryptor());
        setDatabasePlatform(databasePlatform);
        addAll(entities);
    }
}
