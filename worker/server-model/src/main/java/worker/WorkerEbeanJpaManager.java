package worker;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EbeanJpaManager;

import fraud.repo.AmlCheckRepo;
import fraud.repo.DefaultAmlCheckRepo;
import fraud.repo.DefaultFraudRepo;
import fraud.repo.FraudRepo;
import gamehub.repo.DefaultRandomRewardInstanceRepo;
import gamehub.repo.DefaultRandomRewardRepo;
import gamehub.repo.RandomRewardInstanceRepo;
import gamehub.repo.RandomRewardRepo;
import io.ebeaninternal.api.SpiEbeanServer;
import io.ebeaninternal.server.deploy.BeanDescriptor;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import payment.repo.DefaultPaymentMethodMetaInfoRepo;
import payment.repo.DefaultPaymentRepo;
import payment.repo.DefaultProviderRepo;
import payment.repo.PaymentMethodMetaInfoRepo;
import payment.repo.PaymentRepo;
import payment.repo.ProviderRepo;
import uam.repo.AccountRepo;
import uam.repo.BrandRepo;
import uam.repo.DefaultAccountRepo;
import uam.repo.DefaultBrandRepo;
import uam.repo.DefaultWalletRepo;
import uam.repo.DefaultWheelOfWinnersRepo;
import uam.repo.WalletRepo;
import uam.repo.WheelOfWinnersRepo;
import worker.repo.DefaultPostbackFilterRepo;
import worker.repo.DefaultWorkerRepo;
import worker.repo.PostbackFilterRepo;
import worker.repo.WorkerRepo;

public abstract class WorkerEbeanJpaManager extends EbeanJpaManager implements WorkerJpaManager {

    private final BrandRepo uamBrandRepo;
    private final payment.repo.BrandRepo paymentBrandRepo;
    private final AccountRepo uamAccountRepo;
    private final payment.repo.AccountRepo paymentAccountRepo;
    private final WalletRepo walletRepo;
    private final PaymentRepo paymentRepo;
    private final fraud.repo.BrandRepo fraudBrandRepo;
    private final fraud.repo.AccountRepo fraudAccountRepo;
    private final FraudRepo fraudRepo;
    private final WorkerRepo workerRepo;
    private final boolean testMode;
    private final DefaultAmlCheckRepo amlCheckRepo;
    private final PaymentMethodMetaInfoRepo paymentMethodMetaInfoRepo;
    private final WheelOfWinnersRepo wheelOfWinnersRepo;
    private final ProviderRepo providerRepo;
    private final RandomRewardInstanceRepo randomRewardInstanceRepo;
    private final RandomRewardRepo randomRewardRepo;
    private final PostbackFilterRepo postbackFilterRepo;

    public WorkerEbeanJpaManager(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            Tracer tracer,
            SpiEbeanServer database,
            ScheduledExecutorService timer,
            boolean testMode) {
        super(props, meterRegistry, tracer, database, timer);
        this.testMode = testMode;
        this.walletRepo = new DefaultWalletRepo(this);
        this.uamBrandRepo = new DefaultBrandRepo(this);
        this.paymentBrandRepo = new payment.repo.DefaultBrandRepo(this);
        this.uamAccountRepo = new DefaultAccountRepo(this);
        this.paymentAccountRepo = new payment.repo.DefaultAccountRepo(this);
        this.paymentRepo = new DefaultPaymentRepo(this);
        this.fraudBrandRepo = new fraud.repo.DefaultBrandRepo(this);
        this.fraudAccountRepo = new fraud.repo.DefaultAccountRepo(this);
        this.fraudRepo = new DefaultFraudRepo(this);
        this.workerRepo = new DefaultWorkerRepo(this);
        this.amlCheckRepo = new DefaultAmlCheckRepo(this);
        this.paymentMethodMetaInfoRepo = new DefaultPaymentMethodMetaInfoRepo(this);
        this.wheelOfWinnersRepo = new DefaultWheelOfWinnersRepo(this);
        this.providerRepo = new DefaultProviderRepo(this);
        this.randomRewardInstanceRepo = new DefaultRandomRewardInstanceRepo(this);
        this.randomRewardRepo = new DefaultRandomRewardRepo(this);
        this.postbackFilterRepo = new DefaultPostbackFilterRepo(this);
    }

    @Override
    public BrandRepo uamBrandRepo() {
        return this.uamBrandRepo;
    }

    @Override
    public payment.repo.BrandRepo paymentBrandRepo() {
        return this.paymentBrandRepo;
    }

    @Override
    public AccountRepo uamAccountRepo() {
        return this.uamAccountRepo;
    }

    @Override
    public payment.repo.AccountRepo paymentAccountRepo() {
        return this.paymentAccountRepo;
    }

    @Override
    public WalletRepo walletRepo() {
        return this.walletRepo;
    }

    @Override
    public PaymentRepo paymentRepo() {
        return this.paymentRepo;
    }

    @Override
    public fraud.repo.BrandRepo fraudBrandRepo() {
        return this.fraudBrandRepo;
    }

    @Override
    public fraud.repo.AccountRepo fraudAccountRepo() {
        return this.fraudAccountRepo;
    }

    @Override
    public FraudRepo fraudRepo() {
        return this.fraudRepo;
    }

    @Override
    public WorkerRepo workerRepo() {
        return this.workerRepo;
    }

    @Override
    public AmlCheckRepo amlCheckRepo() {
        return amlCheckRepo;
    }

    @Override
    public PaymentMethodMetaInfoRepo paymentMethodMetaInfoRepo() {
        return paymentMethodMetaInfoRepo;
    }

    public WheelOfWinnersRepo wheelOfWinnersRepo() {
        return wheelOfWinnersRepo;
    }

    @Override
    public ProviderRepo providerRepo() {
        return providerRepo;
    }

    @Override
    public RandomRewardInstanceRepo randomRewardInstanceRepo() {
        return randomRewardInstanceRepo;
    }

    @Override
    public RandomRewardRepo randomRewardRepo() {
        return randomRewardRepo;
    }

    @Override
    public PostbackFilterRepo postbackFilterRepo() {
        return postbackFilterRepo;
    }

    @Override
    public List<BeanDescriptor<?>> descriptors() {
        // this hack is done to in a way to remove duplicate entities so the one with more colums will left
        if (testMode) {
            LinkedHashMap<String, BeanDescriptor<?>> tableToDescriptor = new LinkedHashMap<>();
            for (BeanDescriptor<?> candidate : super.descriptors()) {
                var current = tableToDescriptor.get(candidate.baseTable());
                if (current == null || current.propertiesAll().size() < candidate.propertiesAll().size()) {
                    tableToDescriptor.put(candidate.baseTable(), candidate);
                }
            }
            return tableToDescriptor.values().stream().toList();
        }
        return super.descriptors();
    }
}
