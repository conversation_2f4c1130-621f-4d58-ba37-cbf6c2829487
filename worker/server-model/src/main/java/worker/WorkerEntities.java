package worker;

import fraud.FraudEntities;
import gamehub.GameHubEntities;
import model.CoreEntities;
import payment.PaymentEntities;
import uam.UamEntities;
import worker.model.CleanUpJobSummary;
import worker.model.ImmutableAccountGameplayInfo;
import worker.model.PostbackFilter;

public class WorkerEntities extends CoreEntities {
    public WorkerEntities() {
        add(ImmutableAccountGameplayInfo.class);
        addAll(new PaymentEntities());
        addAll(new FraudEntities());
        addAll(new UamEntities());
        addAll(new GameHubEntities());
        add(PostbackFilter.class);
        add(CleanUpJobSummary.class);
    }
}
