package worker;

import com.turbospaces.ebean.JpaManager;

import fraud.repo.AmlCheckRepo;
import fraud.repo.FraudRepo;
import gamehub.repo.RandomRewardInstanceRepo;
import gamehub.repo.RandomRewardRepo;
import payment.repo.PaymentMethodMetaInfoRepo;
import payment.repo.PaymentRepo;
import payment.repo.ProviderRepo;
import uam.repo.AccountRepo;
import uam.repo.BrandRepo;
import uam.repo.WalletRepo;
import uam.repo.WheelOfWinnersRepo;
import worker.repo.PostbackFilterRepo;
import worker.repo.WorkerRepo;

public interface WorkerJpaManager extends JpaManager {

    BrandRepo uamBrandRepo();
    payment.repo.BrandRepo paymentBrandRepo();
    AccountRepo uamAccountRepo();
    payment.repo.AccountRepo paymentAccountRepo();
    WalletRepo walletRepo();
    PaymentRepo paymentRepo();
    fraud.repo.BrandRepo fraudBrandRepo();
    fraud.repo.AccountRepo fraudAccountRepo();
    FraudRepo fraudRepo();
    WorkerRepo workerRepo();
    AmlCheckRepo amlCheckRepo();
    PaymentMethodMetaInfoRepo paymentMethodMetaInfoRepo();
    WheelOfWinnersRepo wheelOfWinnersRepo();
    ProviderRepo providerRepo();
    RandomRewardInstanceRepo randomRewardInstanceRepo();
    RandomRewardRepo randomRewardRepo();
    PostbackFilterRepo postbackFilterRepo();
}
