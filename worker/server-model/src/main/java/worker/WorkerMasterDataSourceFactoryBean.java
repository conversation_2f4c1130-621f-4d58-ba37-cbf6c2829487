package worker;

import javax.sql.DataSource;

import org.springframework.cloud.service.ServiceInfo;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;

import io.micrometer.core.instrument.MeterRegistry;

public class WorkerMasterDataSourceFactoryBean extends HikariDataSourceFactoryBean implements WorkerMasterDatasourceProvider<DataSource> {
    public WorkerMasterDataSourceFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, ServiceInfo si) {
        super(props, meterRegistry, si);
    }
}