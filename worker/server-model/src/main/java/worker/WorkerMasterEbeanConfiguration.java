package worker;

import javax.sql.DataSource;

import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.config.EncryptKeyManager;
import io.ebean.config.dbplatform.DatabasePlatform;

import java.util.LinkedList;

public class WorkerMasterEbeanConfiguration extends WorkerEbeanConfiguration implements WorkerMasterEbeanConfigurationProvider {

    public WorkerMasterEbeanConfiguration(DataSource dataSource, ApplicationProperties props, EncryptKeyManager encryptKeyManager) throws Exception {
        super(dataSource, props, encryptKeyManager);
    }

    public WorkerMasterEbeanConfiguration(
            DataSource ds,
            ApplicationProperties props,
            EncryptKeyManager encryptKeyManager,
            DatabasePlatform databasePlatform) {
        this(ds, props, encryptKeyManager, databasePlatform, new WorkerEntities());
    }

    public WorkerMasterEbeanConfiguration(
            DataSource ds,
            ApplicationProperties props,
            EncryptKeyManager encryptKeyManager,
            DatabasePlatform databasePlatform,
            LinkedList<Class<?>> entities) {
        super(ds, props, encryptKeyManager, databasePlatform, entities);
    }

    @Override
    public WorkerEbeanConfiguration getObject() {
        return this;
    }

    @Override
    public Class<?> getObjectType() {
        return WorkerEbeanConfiguration.class;
    }
}