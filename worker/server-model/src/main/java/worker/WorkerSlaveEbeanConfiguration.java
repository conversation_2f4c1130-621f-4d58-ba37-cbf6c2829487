package worker;

import javax.sql.DataSource;

import com.turbospaces.cfg.ApplicationProperties;

import io.ebean.config.EncryptKeyManager;
import io.ebean.config.dbplatform.DatabasePlatform;

public class WorkerSlaveEbeanConfiguration extends WorkerEbeanConfiguration implements WorkerSlaveEbeanConfigurationProvider {

    public WorkerSlaveEbeanConfiguration(DataSource dataSource, ApplicationProperties props, EncryptKeyManager encryptKeyManager) throws Exception {
        super(dataSource, props, encryptKeyManager);
    }

    public WorkerSlaveEbeanConfiguration(
            DataSource ds,
            ApplicationProperties props,
            EncryptKeyManager encryptKeyManager,
            DatabasePlatform databasePlatform) {
        super(ds, props, encryptKeyManager, databasePlatform, new WorkerEntities());
    }

    @Override
    public WorkerEbeanConfiguration getObject() {
        return this;
    }
    
    @Override
    public Class<?> getObjectType() {
        return WorkerEbeanConfiguration.class;
    }
}