package worker;

import java.util.concurrent.ScheduledExecutorService;

import com.turbospaces.cfg.ApplicationProperties;

import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class WorkerSlaveEbeanJpaManager extends WorkerEbeanJpaManager {
    public WorkerSlaveEbeanJpaManager(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, SpiEbeanServer database, ScheduledExecutorService timer, boolean testMode) {
        super(props, meterRegistry, tracer, database, timer, testMode);
    }
}
