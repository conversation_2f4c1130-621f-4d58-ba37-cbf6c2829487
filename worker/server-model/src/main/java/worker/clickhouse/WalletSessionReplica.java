package worker.clickhouse;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;

import common.CoreConstraints;
import io.ebean.annotation.DbJsonB;
import io.ebean.annotation.StorageEngine;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import uam.model.WalletSession;
import uam.model.WalletSessionTypeSpec;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "uam_wallet_sessions")
@StorageEngine("ENGINE = ReplacingMergeTree(version) PARTITION BY toYYYYMM(at) ORDER BY (at, currency, type, brand_id, product_id, account_id, id)")
public class WalletSessionReplica {
    private BigInteger id;
    private Long brandId;
    private LocalDate at;
    private String source;
    private String sessionId;
    private String externalSessionId;
    private String currency;
    private BigInteger accountId;
    @DbJsonB
    private String transactions;
    private BigDecimal profit = BigDecimal.ZERO;
    private BigDecimal loss = BigDecimal.ZERO;
    private Long productId = 0L;
    private Long xpLevelId = 0L;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal balance = BigDecimal.ZERO;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal unplayedBalance = BigDecimal.ZERO;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal redeemableBalance = BigDecimal.ZERO;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal pendingWithdraw = BigDecimal.ZERO;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal bonusBalance = BigDecimal.ZERO;
    private String type = WalletSessionTypeSpec.UNKNOWN.code();
    private int version;
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;

    public WalletSessionReplica(WalletSession session, CommonObjectMapper mapper) {
        this.id = BigInteger.valueOf(session.getPk().getId());
        this.brandId = Long.valueOf(Objects.nonNull(session.getProduct()) ? session.getProduct().getBrand().getId() : session.getAccount().getBrand().getId());
        this.at = session.getPk().getAt();
        this.source = session.getSource();
        this.sessionId = session.getSessionId();
        this.externalSessionId = session.getExternalSessionId();
        this.currency = session.getCurrency();
        this.accountId = BigInteger.valueOf(session.getAccount().getId());
        try {
            this.transactions = mapper.writeValueAsString(session.getTransactions());
        } catch (JsonProcessingException err) {
            ExceptionUtils.wrapAndThrow(err);
        }
        this.profit = session.getProfit();
        this.loss = session.getLoss();
        if (Objects.nonNull(session.getProduct())) {
            this.productId = Long.valueOf(session.getProduct().getId());
        }
        if (Objects.nonNull(session.getGoldLevel())) {
            this.xpLevelId = Long.valueOf(session.getGoldLevel().getId());
        }
        if (Objects.nonNull(session.getBalance())) {
            this.balance = session.getBalance();
        }
        if (Objects.nonNull(session.getUnplayedBalance())) {
            this.unplayedBalance = session.getUnplayedBalance();
        }
        if (Objects.nonNull(session.getRedeemableBalance())) {
            this.redeemableBalance = session.getRedeemableBalance();
        }
        if (Objects.nonNull(session.getPendingWithdraw())) {
            this.pendingWithdraw = session.getPendingWithdraw();
        }
        if (Objects.nonNull(session.getBonusBalance())) {
            this.bonusBalance = session.getBonusBalance();
        }
        if (Objects.nonNull(session.getType())) {
            this.type = session.getType().code();
        }
        this.version = session.getVersion();
        this.createdAt = PlatformUtil.limitUpperUint32DateTime(session.getCreatedAt());
        this.modifiedAt = PlatformUtil.limitUpperUint32DateTime(session.getModifiedAt());
    }
}
