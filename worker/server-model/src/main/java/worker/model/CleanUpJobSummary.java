package worker.model;

import java.math.BigDecimal;
import java.util.Date;

import io.ebean.annotation.Index;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(
        name = "clean_up_job_summary",
        schema = Schemas.WORKER
)
public class CleanUpJobSummary implements BrandIdAware {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private Integer brandId;

    @Column(nullable = false)
    private Date startedAt;

    @Index
    private Date endedAt;

    @Column(nullable = false)
    private Long totalUsersProcessed;

    @Column(nullable = false)
    private Long usersSentFirstWarning;

    @Column(nullable = false)
    private Long usersSentSecondWarning;

    @Column(nullable = false)
    private Long usersReactivatedAfterFirstWarning;

    @Column(nullable = false)
    private Long usersReactivatedAfterSecondWarning;

    @Column(nullable = false)
    private Long usersCleanedUp;

    @Column(nullable = false)
    private BigDecimal totalGcExpired;

    @Column(nullable = false)
    private BigDecimal totalScExpired;

    @Column(nullable = false)
    private Boolean ok;
}
