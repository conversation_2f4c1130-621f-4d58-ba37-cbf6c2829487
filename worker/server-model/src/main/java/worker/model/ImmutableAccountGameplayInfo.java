package worker.model;

import io.ebean.annotation.Index;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;
import uam.model.AccountGameplayInfo;
import uam.model.AccountMetaInfo;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "account_gameplay_info", schema = Schemas.UAM)
public class ImmutableAccountGameplayInfo {
    @Id
    private Long id;

    @Column
    @Index
    private Date lastGameplay;

    @OneToOne
    @JoinColumn(name = "id")
    private AccountMetaInfo metaInfo;
}
