-- apply changes
create table if not exists uam_wallet_sessions (
  id                            UInt64,
  brand_id                      UInt32,
  at                            Date,
  source                        LowCardinality(String),
  session_id                    String,
  currency                      LowCardinality(String),
  account_id                    UInt64,
  profit                        Decimal(16,2),
  loss                          Decimal(16,2),
  product_id                    UInt32,
  type                          LowCardinality(String),
  version                       UInt32,
  created_at                    DateTime,
  modified_at                   DateTime
) ENGINE = ReplacingMergeTree(version)
PARTITION BY toYYYYMM(at)
ORDER BY (at, currency, type, brand_id, product_id, account_id, id);
