-- apply changes
alter table uam_wallet_sessions add column if not exists transactions Nullable(String);
alter table uam_wallet_sessions add column if not exists xp_level_id Nullable(UInt32);
alter table uam_wallet_sessions add column if not exists balance Nullable(Decimal(16,2));
alter table uam_wallet_sessions add column if not exists unplayed_balance Nullable(Decimal(16,2));
alter table uam_wallet_sessions add column if not exists redeemable_balance Nullable(Decimal(16,2));
alter table uam_wallet_sessions add column if not exists pending_withdraw Nullable(Decimal(16,2));
alter table uam_wallet_sessions add column if not exists bonus_balance Nullable(Decimal(16,2));
