<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="uam_wallet_sessions" pkName="pk_wallet_sessions">
            <column name="id" type="bigint"/>
            <column name="brand_id" type="bigint"/>
            <column name="at" type="date"/>
            <column name="source" type="varchar"/>
            <column name="session_id" type="varchar"/>
            <column name="currency" type="varchar"/>
            <column name="account_id" type="bigint"/>
            <column name="profit" type="decimal"/>
            <column name="loss" type="decimal"/>
            <column name="product_id" type="bigint"/>
            <column name="type" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="localdatetime"/>
            <column name="modified_at" type="localdatetime"/>
        </createTable>
    </changeSet>
</migration>