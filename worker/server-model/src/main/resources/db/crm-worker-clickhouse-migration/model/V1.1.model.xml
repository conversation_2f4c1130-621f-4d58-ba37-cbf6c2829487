<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="uam_wallet_sessions">
            <column name="transactions" type="jsonb"/>
            <column name="gold_level_id" type="bigint"/>
            <column name="balance" type="decimal(16,2)"/>
            <column name="unplayed_balance" type="decimal(16,2)"/>
            <column name="redeemable_balance" type="decimal(16,2)"/>
            <column name="pending_withdraw" type="decimal(16,2)"/>
            <column name="bonus_balance" type="decimal(16,2)"/>
        </addColumn>
    </changeSet>
</migration>