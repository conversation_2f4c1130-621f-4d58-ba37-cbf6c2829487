-- apply changes
create table if not exists worker.postback_filters
(
    id             bigint generated by default as identity not null,
    account_id     bigint                                  not null,
    postback_type  varchar(255)                            not null,
    event_name     varchar(255)                            not null,
    jsonpath_query varchar(4000),
    created_at     timestamptz                             not null,
    modified_at    timestamptz                             not null,
    constraint uq_postback_filters_account_id_brand_id unique (account_id, postback_type, event_name),
    constraint pk_postback_filters primary key (id)
);

-- foreign keys and indices
create index if not exists ix_postback_filters_account_id on worker.postback_filters (account_id);
alter table worker.postback_filters
    add constraint fk_postback_filters_account_id foreign key (account_id) references uam.accounts (id) on delete restrict on update restrict;
