-- apply changes
create table if not exists worker.clean_up_job_summary (
  id                            bigint generated by default as identity not null,
  started_at                    timestamptz not null,
  ended_at                      timestamptz not null,
  total_users_processed         bigint not null,
  users_sent_first_warning      bigint not null,
  users_sent_second_warning     bigint not null,
  users_reactivated_after_first_warning bigint not null,
  users_reactivated_after_second_warning bigint not null,
  users_cleaned_up              bigint not null,
  ok                            boolean not null,
  total_gc_expired              decimal(16,3) not null,
  total_sc_expired              decimal(16,3) not null,
  constraint pk_clean_up_job_summary primary key (id)
);

-- foreign keys and indices
create index if not exists ix_clean_up_job_summary_ended_at on worker.clean_up_job_summary (ended_at);
