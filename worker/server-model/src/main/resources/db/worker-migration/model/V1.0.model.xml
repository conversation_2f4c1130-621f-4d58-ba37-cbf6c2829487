<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createSchema name="core"/>
        <createSchema name="payment"/>
        <createSchema name="uam"/>
        <createTable name="uam.accounts" pkName="pk_accounts">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="hash" type="varchar" notnull="true"/>
            <column name="bot" type="boolean" defaultValue="false" notnull="true"/>
            <column name="deleted" type="boolean" defaultValue="false" notnull="true"/>
            <column name="at" type="date" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_accounts_brand_id" foreignKeyIndex="ix_accounts_brand_id"/>
            <column name="person_id" type="bigint" notnull="true" references="uam.persons.id" foreignKeyName="fk_accounts_person_id" foreignKeyIndex="ix_accounts_person_id"/>
            <column name="username" type="varchar(50)" notnull="true"/>
            <column name="email_mask" type="varbinary" notnull="true"/>
            <column name="real_email_mask" type="varbinary" notnull="true"/>
            <column name="name" type="varchar(4000)"/>
            <column name="phone_number_mask" type="varbinary"/>
            <column name="restrictions" type="varchar[]"/>
            <column name="mode" type="varchar(22)" notnull="true" checkConstraint="check ( mode in ('default','gold','gold_free','sweepstake','sweepstake_preview','sweepstake_preview_web'))" checkConstraintName="ck_accounts_mode"/>
            <column name="email_verified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="admin" type="boolean" defaultValue="false" notnull="true"/>
            <column name="kyc" type="varchar(15)" notnull="true" checkConstraint="check ( kyc in ('initial','in_review','confirmed','id_confirmed','doc_review','doc_declined','session_expired','declined','blocked'))" checkConstraintName="ck_accounts_kyc"/>
            <column name="status" type="varchar(24)" notnull="true" checkConstraint="check ( status in ('default','requires_jumio_kyc','under_investigation','pending_redeem_review','bw_pending_redeem_review','pending_verification'))" checkConstraintName="ck_accounts_status"/>
            <column name="guest" type="boolean" defaultValue="false" notnull="true"/>
            <column name="locked" type="boolean" defaultValue="false" notnull="true"/>
            <column name="vip_level_purchase" type="varchar(7)" checkConstraint="check ( vip_level_purchase in ('class_a','class_b','class_c','class_d'))" checkConstraintName="ck_accounts_vip_level_purchase"/>
            <column name="vip_level_override_purchase" type="varchar(7)" checkConstraint="check ( vip_level_override_purchase in ('class_a','class_b','class_c','class_d'))" checkConstraintName="ck_accounts_vip_level_override_purchase"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_push_tokens" pkName="pk_account_push_tokens">
            <column name="account_id" type="bigint" notnull="true" primaryKey="true"/>
            <column name="token_id" type="bigint" notnull="true" primaryKey="true"/>
            <foreignKey name="fk_account_push_tokens_accounts" columnNames="account_id" refColumnNames="id" refTableName="uam.accounts" indexName="ix_account_push_tokens_accounts"/>
            <foreignKey name="fk_account_push_tokens_push_tokens" columnNames="token_id" refColumnNames="id" refTableName="uam.push_tokens" indexName="ix_account_push_tokens_push_tokens"/>
        </createTable>
        <createTable name="uam.account_visitor_metas" pkName="pk_account_visitor_metas">
            <column name="account_id" type="bigint" notnull="true" primaryKey="true"/>
            <column name="meta_id" type="bigint" notnull="true" primaryKey="true"/>
            <foreignKey name="fk_account_visitor_metas_accounts" columnNames="account_id" refColumnNames="id" refTableName="uam.accounts" indexName="ix_account_visitor_metas_accounts"/>
            <foreignKey name="fk_account_visitor_metas_visitors_meta" columnNames="meta_id" refColumnNames="id" refTableName="uam.visitors_meta" indexName="ix_account_visitor_metas_visitors_meta"/>
        </createTable>
        <createTable name="uam.account_accepted_rules_history" pkName="pk_account_accepted_rules_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_accepted_rules_history_account_id" foreignKeyIndex="ix_account_accepted_rules_history_account_id"/>
            <column name="type" type="varchar(2)" checkConstraint="check ( type in ('tc','sr','pp'))" checkConstraintName="ck_account_accepted_rules_history_type"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_accepted_rules_history_account_id_type_version" columnNames="account_id,type,version" oneToOne="false" nullableColumns="type"/>
        </createTable>
        <createTable name="uam.account_audit" pkName="pk_account_audit">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="type" type="varchar(15)" checkConstraint="check ( type in ('sign_in','password_change','password_reset','confirm_email','confirm_phone','email_change','enable_2fa','disable_2fa'))" checkConstraintName="ck_account_audit_type"/>
            <column name="ip" type="varchar(45)"/>
            <column name="user_agent" type="varchar(4000)"/>
            <column name="location" type="jsonb"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_audit_account_id" foreignKeyIndex="ix_account_audit_account_id"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_balances" pkName="pk_account_balances">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_balances_account_id" foreignKeyIndex="ix_account_balances_account_id"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <uniqueConstraint name="uq_account_balances_account_id_currency" columnNames="account_id,currency" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.account_category_tags" pkName="pk_account_category_tags">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_category_tags_account_id" foreignKeyIndex="ix_account_category_tags_account_id"/>
            <column name="category" type="varchar" notnull="true"/>
            <column name="tags" type="varchar[]"/>
        </createTable>
        <createTable name="uam.account_credentials" pkName="pk_account_credentials">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_credentials_id"/>
            <column name="algorithm" type="varchar(10)"/>
            <column name="salt" type="varchar(32)"/>
            <column name="hash" type="varchar(512)"/>
            <column name="opts" type="jsonb"/>
            <column name="change" type="boolean" defaultValue="false" notnull="true"/>
            <column name="retry_attempt" type="integer" notnull="true"/>
            <column name="retry_after" type="integer" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_credentials_history" pkName="pk_account_credentials_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_account_credentials_history_account_id" foreignKeyIndex="ix_account_credentials_history_account_id"/>
            <column name="algorithm" type="varchar(10)"/>
            <column name="hash" type="varchar(512)"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_daily_bonuses" pkName="pk_account_daily_bonuses">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_daily_bonuses_id"/>
            <column name="at" type="date"/>
            <column name="next_bonus_at" type="localdatetime"/>
            <column name="next_bonus_day" type="integer"/>
            <column name="accepted_count" type="integer" notnull="true"/>
            <column name="last_accept" type="date"/>
            <column name="last_reminder" type="date"/>
            <column name="day0t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_day0t_id" foreignKeyIndex="ix_account_daily_bonuses_day0t_id"/>
            <column name="day1t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_day1t_id" foreignKeyIndex="ix_account_daily_bonuses_day1t_id"/>
            <column name="day2t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_day2t_id" foreignKeyIndex="ix_account_daily_bonuses_day2t_id"/>
            <column name="day3t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_day3t_id" foreignKeyIndex="ix_account_daily_bonuses_day3t_id"/>
            <column name="day4t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_day4t_id" foreignKeyIndex="ix_account_daily_bonuses_day4t_id"/>
            <column name="day5t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_day5t_id" foreignKeyIndex="ix_account_daily_bonuses_day5t_id"/>
            <column name="day6t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_day6t_id" foreignKeyIndex="ix_account_daily_bonuses_day6t_id"/>
            <column name="day7t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_day7t_id" foreignKeyIndex="ix_account_daily_bonuses_day7t_id"/>
            <column name="w2t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_w2t_id" foreignKeyIndex="ix_account_daily_bonuses_w2t_id"/>
            <column name="w3t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_w3t_id" foreignKeyIndex="ix_account_daily_bonuses_w3t_id"/>
            <column name="w4t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_w4t_id" foreignKeyIndex="ix_account_daily_bonuses_w4t_id"/>
            <column name="m2t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_m2t_id" foreignKeyIndex="ix_account_daily_bonuses_m2t_id"/>
            <column name="m3t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_m3t_id" foreignKeyIndex="ix_account_daily_bonuses_m3t_id"/>
            <column name="m4t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_m4t_id" foreignKeyIndex="ix_account_daily_bonuses_m4t_id"/>
            <column name="m5t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_m5t_id" foreignKeyIndex="ix_account_daily_bonuses_m5t_id"/>
            <column name="m6t_id" type="bigint" references="uam.daily_bonus_templates.id" foreignKeyName="fk_account_daily_bonuses_m6t_id" foreignKeyIndex="ix_account_daily_bonuses_m6t_id"/>
            <column name="day0s" type="varchar(8)" checkConstraint="check ( day0s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_day0s"/>
            <column name="day1s" type="varchar(8)" checkConstraint="check ( day1s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_day1s"/>
            <column name="day2s" type="varchar(8)" checkConstraint="check ( day2s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_day2s"/>
            <column name="day3s" type="varchar(8)" checkConstraint="check ( day3s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_day3s"/>
            <column name="day4s" type="varchar(8)" checkConstraint="check ( day4s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_day4s"/>
            <column name="day5s" type="varchar(8)" checkConstraint="check ( day5s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_day5s"/>
            <column name="day6s" type="varchar(8)" checkConstraint="check ( day6s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_day6s"/>
            <column name="day7s" type="varchar(8)" checkConstraint="check ( day7s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_day7s"/>
            <column name="w2s" type="varchar(8)" checkConstraint="check ( w2s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_w2s"/>
            <column name="w3s" type="varchar(8)" checkConstraint="check ( w3s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_w3s"/>
            <column name="w4s" type="varchar(8)" checkConstraint="check ( w4s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_w4s"/>
            <column name="m2s" type="varchar(8)" checkConstraint="check ( m2s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_m2s"/>
            <column name="m3s" type="varchar(8)" checkConstraint="check ( m3s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_m3s"/>
            <column name="m4s" type="varchar(8)" checkConstraint="check ( m4s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_m4s"/>
            <column name="m5s" type="varchar(8)" checkConstraint="check ( m5s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_m5s"/>
            <column name="m6s" type="varchar(8)" checkConstraint="check ( m6s in ('initial','accepted'))" checkConstraintName="ck_account_daily_bonuses_m6s"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_engagement_info" pkName="pk_account_engagement_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_engagement_info_id"/>
            <column name="vip_level_id" type="bigint" notnull="true" references="core.vip_levels.id" foreignKeyName="fk_account_engagement_info_vip_level_id" foreignKeyIndex="ix_account_engagement_info_vip_level_id"/>
            <column name="xp_level_id" type="bigint" notnull="true" references="core.xp_levels.id" foreignKeyName="fk_account_engagement_info_xp_level_id" foreignKeyIndex="ix_account_engagement_info_xp_level_id"/>
            <column name="vip_level_points" type="decimal(16,2)" notnull="true"/>
            <column name="gold_points" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_points" type="decimal(16,2)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_events" pkName="pk_account_events">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_events_account_id" foreignKeyIndex="ix_account_events_account_id"/>
            <column name="event_type" type="varchar(39)" notnull="true" checkConstraint="check ( event_type in ('set_account_info','set_account_kyc','set_account_mode','set_account_soft_kyc_info','set_account_purchase_limit','set_account_segmentation','set_account_segmentation_mass','account_deduct','account_reward','account_reward_mass','account_payment_method','account_lock','account_unlock','account_lock_mass','redeem_confirm_mass','redeem_decline_mass','redeem_lock_mass','redeem_unlock_mass','reset_account_purchase_withdraw_methods','reset_account_purchase_limit','send_email_mass','mass_chargeback','unknown'))" checkConstraintName="ck_account_events_event_type"/>
            <column name="message" type="varchar(2000)"/>
            <column name="reference" type="varchar"/>
            <column name="created_by" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_experience_level_history" pkName="pk_account_experience_level_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_experience_level_history_account_id" foreignKeyIndex="ix_account_experience_level_history_account_id"/>
            <column name="level" type="integer" notnull="true"/>
            <column name="points" type="decimal(16)" notnull="true"/>
            <column name="type" type="varchar(10)" notnull="true" checkConstraint="check ( type in ('free_level','real_level','vip_level'))" checkConstraintName="ck_account_experience_level_history_type"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_fraud_info" pkName="pk_account_fraud_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_fraud_info_id"/>
            <column name="last_sign_in_fraud_score" type="integer"/>
            <column name="sign_up_fraud_score" type="integer"/>
            <column name="last_fraud_score" type="integer"/>
            <column name="sign_up_cookie_hash" type="varchar"/>
            <column name="last_sign_in_cookie_hash" type="varchar"/>
            <column name="last_withdraw_cookie_hash" type="varchar"/>
        </createTable>
        <createTable name="uam.account_gameplay_info" pkName="pk_account_gameplay_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_gameplay_info_id"/>
            <column name="first_gc_casino_product_id" type="integer" references="core.products.id" foreignKeyName="fk_account_gameplay_info_first_gc_casino_product_id" foreignKeyIndex="ix_account_gameplay_info_first_gc_casino_product_id"/>
            <column name="first_sc_casino_product_id" type="integer" references="core.products.id" foreignKeyName="fk_account_gameplay_info_first_sc_casino_product_id" foreignKeyIndex="ix_account_gameplay_info_first_sc_casino_product_id"/>
            <column name="first_gc_lotto_product_id" type="integer" references="core.products.id" foreignKeyName="fk_account_gameplay_info_first_gc_lotto_product_id" foreignKeyIndex="ix_account_gameplay_info_first_gc_lotto_product_id"/>
            <column name="first_sc_lotto_product_id" type="integer" references="core.products.id" foreignKeyName="fk_account_gameplay_info_first_sc_lotto_product_id" foreignKeyIndex="ix_account_gameplay_info_first_sc_lotto_product_id"/>
            <column name="first_gc_bingo_product_id" type="integer" references="core.products.id" foreignKeyName="fk_account_gameplay_info_first_gc_bingo_product_id" foreignKeyIndex="ix_account_gameplay_info_first_gc_bingo_product_id"/>
            <column name="first_sc_bingo_product_id" type="integer" references="core.products.id" foreignKeyName="fk_account_gameplay_info_first_sc_bingo_product_id" foreignKeyIndex="ix_account_gameplay_info_first_sc_bingo_product_id"/>
            <column name="last_gc_casino_gameplay" type="timestamp"/>
            <column name="last_sc_casino_gameplay" type="timestamp"/>
            <column name="last_gc_lotto_gameplay" type="timestamp"/>
            <column name="last_sc_lotto_gameplay" type="timestamp"/>
            <column name="last_gc_bingo_gameplay" type="timestamp"/>
            <column name="last_sc_bingo_gameplay" type="timestamp"/>
            <column name="first_gc_casino_gameplay" type="timestamp"/>
            <column name="first_sc_casino_gameplay" type="timestamp"/>
            <column name="first_gc_lotto_gameplay" type="timestamp"/>
            <column name="first_sc_lotto_gameplay" type="timestamp"/>
            <column name="first_gc_bingo_gameplay" type="timestamp"/>
            <column name="first_sc_bingo_gameplay" type="timestamp"/>
            <column name="total_gc_game_round_count" type="integer"/>
            <column name="total_sc_game_round_count" type="integer"/>
            <column name="total_gc_game_round_count_1d" type="integer"/>
            <column name="total_sc_game_round_count_1d" type="integer"/>
            <column name="total_gc_game_round_count_2d" type="integer"/>
            <column name="total_sc_game_round_count_2d" type="integer"/>
            <column name="total_gc_game_round_count_7d" type="integer"/>
            <column name="total_sc_game_round_count_7d" type="integer"/>
            <column name="total_gc_wager_amount" type="decimal(16,2)"/>
            <column name="total_sc_wager_amount" type="decimal(16,2)"/>
            <column name="total_gc_win_amount" type="decimal(16,2)"/>
            <column name="total_sc_win_amount" type="decimal(16,2)"/>
            <column name="total_gc_wager_amount_1d" type="decimal(16,2)"/>
            <column name="total_sc_wager_amount_1d" type="decimal(16,2)"/>
            <column name="total_gc_win_amount_1d" type="decimal(16,2)"/>
            <column name="total_sc_win_amount_1d" type="decimal(16,2)"/>
            <column name="total_gc_wager_amount_2d" type="decimal(16,2)"/>
            <column name="total_sc_wager_amount_2d" type="decimal(16,2)"/>
            <column name="total_gc_win_amount_2d" type="decimal(16,2)"/>
            <column name="total_sc_win_amount_2d" type="decimal(16,2)"/>
            <column name="total_gc_wager_amount_7d" type="decimal(16,2)"/>
            <column name="total_sc_wager_amount_7d" type="decimal(16,2)"/>
            <column name="total_gc_win_amount_7d" type="decimal(16,2)"/>
            <column name="total_sc_win_amount_7d" type="decimal(16,2)"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_invitation_dropdowns" pkName="pk_account_invitation_dropdowns">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="inviter_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_account_invitation_dropdowns_inviter_id" foreignKeyIndex="ix_account_invitation_dropdowns_inviter_id"/>
            <column name="actor_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_account_invitation_dropdowns_actor_id" foreignKeyIndex="ix_account_invitation_dropdowns_actor_id"/>
            <column name="rewarded" type="boolean" defaultValue="false" notnull="true"/>
            <column name="sweepstake" type="decimal(16,2)" notnull="true"/>
            <column name="gold" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_sign_up" type="decimal(16,2)" notnull="true"/>
            <column name="gold_sign_up" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_first_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="gold_first_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="gold_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_first_withdraw" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_withdraw" type="decimal(16,2)" notnull="true"/>
            <column name="purchase_aggr" type="decimal(16,2)" notnull="true"/>
            <column name="purchases" type="integer" notnull="true"/>
            <column name="withdraws" type="integer" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_invitation_dropdowns_inviter_id_actor_id" columnNames="inviter_id,actor_id" oneToOne="false" nullableColumns="inviter_id,actor_id"/>
        </createTable>
        <createTable name="uam.account_invitation_info" pkName="pk_account_invitation_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_invitation_info_id"/>
            <column name="invited" type="integer" notnull="true"/>
            <column name="sweepstake" type="decimal(16,2)" notnull="true"/>
            <column name="gold" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_sign_up" type="decimal(16,2)" notnull="true"/>
            <column name="gold_sign_up" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_first_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="gold_first_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="gold_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_first_withdraw" type="decimal(16,2)" notnull="true"/>
            <column name="sweepstake_withdraw" type="decimal(16,2)" notnull="true"/>
            <column name="purchase_aggr" type="decimal(16,2)" notnull="true"/>
            <column name="purchases" type="integer" notnull="true"/>
            <column name="withdraws" type="integer" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_jackpot_info" pkName="pk_account_jackpot_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_jackpot_info_id"/>
            <column name="opted_in" type="boolean" defaultValue="false" notnull="true"/>
            <column name="auto_opted_out" type="boolean" defaultValue="false" notnull="true"/>
            <column name="gc_contribute_amount" type="decimal(16,2)"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.account_meta_info" pkName="pk_account_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" uniqueOneToOne="uq_account_meta_info_account_id" references="uam.accounts.id" foreignKeyName="fk_account_meta_info_account_id"/>
            <column name="first_payment_order_id" type="bigint" uniqueOneToOne="uq_account_meta_info_first_payment_order_id" references="payment.payment_orders.id" foreignKeyName="fk_account_meta_info_first_payment_order_id"/>
            <column name="first_successful_payment_order_id" type="bigint" uniqueOneToOne="uq_account_meta_info_first_successful_payment_order_id" references="payment.payment_orders.id" foreignKeyName="fk_account_meta_info_first_successful_payment_order_id"/>
            <column name="last_payment_order_id" type="bigint" uniqueOneToOne="uq_account_meta_info_last_payment_order_id" references="payment.payment_orders.id" foreignKeyName="fk_account_meta_info_last_payment_order_id"/>
            <column name="first_withdraw_id" type="bigint" uniqueOneToOne="uq_account_meta_info_first_withdraw_id" references="payment.withdraw_money_requests.id" foreignKeyName="fk_account_meta_info_first_withdraw_id"/>
            <column name="last_withdraw_id" type="bigint" uniqueOneToOne="uq_account_meta_info_last_withdraw_id" references="payment.withdraw_money_requests.id" foreignKeyName="fk_account_meta_info_last_withdraw_id"/>
            <column name="deposit_count" type="integer" notnull="true"/>
            <column name="withdraw_count" type="integer" notnull="true"/>
            <column name="total_deposit_amount" type="decimal(16,2)"/>
            <column name="total_withdraw_amount" type="decimal(16,2)"/>
            <column name="providers_purchase" type="varchar[]"/>
            <column name="providers_withdraw" type="varchar[]"/>
            <column name="skrill_emails" type="varchar[]"/>
            <column name="card_holder_names" type="varchar[]"/>
            <column name="card_bins" type="varchar[]"/>
            <column name="card_last_fours" type="varchar[]"/>
            <column name="offers" type="varchar[]"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_meta_info" pkName="pk_account_meta_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_meta_info_id"/>
            <column name="last_sign_in_ip" type="varchar(45)"/>
            <column name="last_sign_in_country" type="varchar(2)"/>
            <column name="last_sign_in_city" type="varchar(4000)"/>
            <column name="last_sign_in_state" type="varchar"/>
            <column name="last_sign_in_user_agent" type="varchar(4000)"/>
            <column name="last_sign_in_method" type="varchar"/>
            <column name="sign_up_method" type="varchar" notnull="true"/>
            <column name="query" type="varchar(4000)"/>
            <column name="sign_up_ip" type="varchar(45)" notnull="true"/>
            <column name="sign_up_country" type="varchar(2)"/>
            <column name="sign_up_city" type="varchar(4000)"/>
            <column name="sign_up_state" type="varchar"/>
            <column name="sign_up_user_agent" type="varchar(4000)"/>
            <column name="advertising_id" type="varchar"/>
            <column name="first_deposit" type="timestamp"/>
            <column name="last_deposit" type="timestamp"/>
            <column name="last_kyc" type="timestamp"/>
            <column name="deposit_count" type="integer" notnull="true"/>
            <column name="total_deposit_amount" type="decimal(16,2)"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_notes" pkName="pk_account_notes">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_notes_account_id" foreignKeyIndex="ix_account_notes_account_id"/>
            <column name="note" type="varchar(4000)" notnull="true"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_account_notes_modified_by_id" foreignKeyIndex="ix_account_notes_modified_by_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_oauths" pkName="pk_account_oauths">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_oauths_account_id" foreignKeyIndex="ix_account_oauths_account_id"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="uuid" type="varchar"/>
        </createTable>
        <createTable name="payment.account_payment_info" pkName="pk_account_payment_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="customer_id" type="varchar" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_payment_info_account_id" foreignKeyIndex="ix_account_payment_info_account_id"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_payment_info_customer_id_provider" columnNames="customer_id,provider" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.payment_methods" pkName="pk_payment_methods">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="remember" type="boolean" defaultValue="false" notnull="true"/>
            <column name="type" type="varchar"/>
            <column name="fingerprint" type="varchar"/>
            <column name="method" type="jsonb"/>
            <column name="card_payment_method_id" type="integer" references="payment.card_payment_method.id" foreignKeyName="fk_payment_methods_card_payment_method_id" foreignKeyIndex="ix_payment_methods_card_payment_method_id"/>
            <column name="ach_payment_method_id" type="integer" references="payment.ach_payment_method.id" foreignKeyName="fk_payment_methods_ach_payment_method_id" foreignKeyIndex="ix_payment_methods_ach_payment_method_id"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_payment_methods_account_id" foreignKeyIndex="ix_payment_methods_account_id"/>
            <column name="deleted" type="boolean" defaultValue="false" notnull="true"/>
            <column name="status" type="varchar(20)" checkConstraint="check ( status in ('verified','not_verified','third_party_verified','blocked','ignore'))" checkConstraintName="ck_payment_methods_status"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_postal_codes" pkName="pk_account_postal_codes">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_postal_codes_account_id" foreignKeyIndex="ix_account_postal_codes_account_id"/>
            <column name="postal_code" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_postal_codes_postal_code" columnNames="postal_code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.account_preferences" pkName="pk_account_preferences">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_preferences_id"/>
            <column name="lang" type="varchar(2)" notnull="true"/>
            <column name="currency" type="varchar(3)"/>
            <column name="timezone" type="varchar(256)"/>
            <column name="do_not_send_emails" type="boolean" defaultValue="false" notnull="true"/>
            <column name="do_not_send_pushes" type="boolean" defaultValue="false" notnull="true"/>
            <column name="auto_lock_withdraw" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.account_purchase_limit" pkName="pk_account_purchase_limit">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="threshold" type="decimal(16,2)" notnull="true"/>
            <column name="days" type="varchar" notnull="true"/>
            <column name="period" type="varchar(7)" checkConstraint="check ( period in ('daily','weekly','monthly'))" checkConstraintName="ck_account_purchase_limit_period"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_account_purchase_limit_account_id" foreignKeyIndex="ix_account_purchase_limit_account_id"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="reason" type="varchar(20)" checkConstraint="check ( reason in ('member_request','suspicious_behaviour','verification_needed','chb_mitigation'))" checkConstraintName="ck_account_purchase_limit_reason"/>
            <column name="limit_end" type="date"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_purchase_limit_days_account_id" columnNames="days,account_id" oneToOne="false" nullableColumns="account_id"/>
        </createTable>
        <createTable name="uam.account_tracking_info" pkName="pk_account_tracking_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_tracking_info_account_id" foreignKeyIndex="ix_account_tracking_info_account_id"/>
            <column name="tracking_id" type="varchar" notnull="true"/>
            <column name="type" type="varchar(7)" notnull="true" checkConstraint="check ( type in ('ios','android'))" checkConstraintName="ck_account_tracking_info_type"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_vip_level_history" pkName="pk_account_vip_level_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_vip_level_history_account_id" foreignKeyIndex="ix_account_vip_level_history_account_id"/>
            <column name="level" type="varchar(7)" checkConstraint="check ( level in ('class_a','class_b','class_c','class_d'))" checkConstraintName="ck_account_vip_level_history_level"/>
            <column name="source" type="varchar(6)" checkConstraint="check ( source in ('auto','manual'))" checkConstraintName="ck_account_vip_level_history_source"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.withdraw_methods" pkName="pk_withdraw_methods">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="status" type="varchar"/>
            <column name="remember" type="boolean" defaultValue="false" notnull="true"/>
            <column name="api_error" type="jsonb"/>
            <column name="temp_token" type="varchar(4000)"/>
            <column name="remote_id" type="varchar"/>
            <column name="method" type="jsonb"/>
            <column name="type" type="varchar(17)" notnull="true" checkConstraint="check ( type in ('ach','sci','pay_pal','skrill','skrill_ach','nuvei','nuvei_mazooma_ach','prizeout','pay_with_my_bank'))" checkConstraintName="ck_withdraw_methods_type"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_withdraw_methods_account_id" foreignKeyIndex="ix_withdraw_methods_account_id"/>
            <column name="email_sent" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_withdraw_methods_account_id_type" columnNames="account_id,type" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.ach_payment_method" withHistory="true" pkName="pk_ach_payment_method">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="account_name" type="varchar"/>
            <column name="account_number" type="varchar"/>
            <column name="account_routing_number" type="varchar"/>
            <column name="account_type" type="varchar(8)" checkConstraint="check ( account_type in ('checking','saving'))" checkConstraintName="ck_ach_payment_method_account_type"/>
            <column name="bank_name" type="varchar"/>
            <column name="payment_provider_id" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.advertising_campaign_info" pkName="pk_advertising_campaign_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="advertising_id" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_advertising_campaign_info_brand_id" foreignKeyIndex="ix_advertising_campaign_info_brand_id"/>
            <column name="campaign_id" type="bigint" references="uam.campaign_templates.id" foreignKeyName="fk_advertising_campaign_info_campaign_id" foreignKeyIndex="ix_advertising_campaign_info_campaign_id"/>
            <column name="platform" type="varchar"/>
            <column name="ip" type="varchar(45)"/>
            <column name="user_agent" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_advertising_campaign_info_brand_id_advertising_id" columnNames="brand_id,advertising_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="core.affiliate_providers" pkName="pk_affiliate_providers">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_affiliate_providers_brand_id" foreignKeyIndex="ix_affiliate_providers_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="name" type="varchar"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_affiliate_providers_modified_by_id" foreignKeyIndex="ix_affiliate_providers_modified_by_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_affiliate_providers_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.bonus_rewards" pkName="pk_bonus_rewards">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_bonus_rewards_account_id" foreignKeyIndex="ix_bonus_rewards_account_id"/>
            <column name="actor_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_bonus_rewards_actor_id" foreignKeyIndex="ix_bonus_rewards_actor_id"/>
            <column name="code" type="varchar(36)" notnull="true"/>
            <column name="product_id" type="integer" notnull="true" references="core.products.id" foreignKeyName="fk_bonus_rewards_product_id" foreignKeyIndex="ix_bonus_rewards_product_id"/>
            <column name="gold_amount" type="decimal(16,2)"/>
            <column name="sweepstake_amount" type="decimal(16,2)"/>
            <column name="fiat_amount" type="decimal(16,2)"/>
            <column name="accepted" type="boolean" defaultValue="false" notnull="true"/>
            <column name="accepted_at" type="date"/>
            <column name="expire_at" type="date"/>
            <column name="at" type="date" notnull="true"/>
            <column name="created_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_bonus_rewards_created_by_id" foreignKeyIndex="ix_bonus_rewards_created_by_id"/>
            <column name="creditor_id" type="bigint" references="uam.reward_creditors.id" foreignKeyName="fk_bonus_rewards_creditor_id" foreignKeyIndex="ix_bonus_rewards_creditor_id"/>
            <column name="campaign_id" type="bigint" references="uam.reward_campaigns.id" foreignKeyName="fk_bonus_rewards_campaign_id" foreignKeyIndex="ix_bonus_rewards_campaign_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_bonus_rewards_account_id_code" columnNames="account_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="core.brands" pkName="pk_brands">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true" unique="uq_brands_name"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="fiat_currency" type="varchar(3)" notnull="true"/>
            <column name="gold_currency" type="varchar(3)" notnull="true"/>
            <column name="sweepstake_currency" type="varchar(3)"/>
            <column name="secret" type="varchar"/>
            <column name="webhook_url" type="varchar(4000)"/>
            <column name="home_page" type="varchar(4000)"/>
        </createTable>
        <createTable name="uam.campaign_templates" pkName="pk_campaign_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_campaign_templates_brand_id" foreignKeyIndex="ix_campaign_templates_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="name" type="varchar"/>
            <column name="partner" type="varchar"/>
            <column name="mode" type="varchar(22)" notnull="true" checkConstraint="check ( mode in ('default','gold','gold_free','sweepstake','sweepstake_preview','sweepstake_preview_web'))" checkConstraintName="ck_campaign_templates_mode"/>
            <column name="vip_points" type="decimal(16,2)"/>
            <column name="gold_money" type="decimal(16,2)"/>
            <column name="sweepstake_money" type="decimal(16,2)"/>
            <column name="fiat_money" type="decimal(16,2)"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_campaign_templates_modified_by_id" foreignKeyIndex="ix_campaign_templates_modified_by_id"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_campaign_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.card_bin_details" pkName="pk_card_bin_details">
            <column name="payment_method_id" type="bigint" primaryKey="true" references="payment.payment_methods.id" foreignKeyName="fk_card_bin_details_payment_method_id"/>
            <column name="bin_bank" type="varchar"/>
            <column name="bin_type" type="varchar"/>
            <column name="bin_level" type="varchar"/>
            <column name="bin_country_code" type="varchar"/>
        </createTable>
        <createTable name="payment.card_bin_info" withHistory="true" identityType="external" pkName="pk_card_bin_info">
            <column name="bin" type="varchar" primaryKey="true"/>
            <column name="bin_bank" type="varchar"/>
            <column name="bin_type" type="varchar"/>
            <column name="bin_level" type="varchar"/>
            <column name="bin_country_code" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.card_payment_method" withHistory="true" pkName="pk_card_payment_method">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand" type="varchar"/>
            <column name="bin" type="varchar"/>
            <column name="last_four" type="varchar"/>
            <column name="expiry_month" type="integer"/>
            <column name="expiry_year" type="integer"/>
            <column name="holder_name" type="varchar"/>
            <column name="cvv2" type="varchar"/>
            <column name="street_match" type="boolean"/>
            <column name="postal_code_match" type="boolean"/>
            <column name="association_avs_response" type="varchar"/>
            <column name="address1" type="varchar"/>
            <column name="address2" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="state" type="varchar"/>
            <column name="zip" type="varchar"/>
            <column name="country" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.card_transaction_details" pkName="pk_card_transaction_details">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="auth_code" type="varchar"/>
            <column name="external_transaction_id" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.chargeback_history" pkName="pk_chargeback_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="status" type="varchar(13)" notnull="true" checkConstraint="check ( status in ('pending','won','lost','chargeback','alert','rdr','partial_match','unknown'))" checkConstraintName="ck_chargeback_history_status"/>
            <column name="date" type="timestamp" notnull="true"/>
            <column name="reason" type="varchar"/>
            <column name="agent_name" type="varchar"/>
            <column name="order_id" type="bigint" references="payment.payment_orders.id" foreignKeyName="fk_chargeback_history_order_id" foreignKeyIndex="ix_chargeback_history_order_id"/>
            <column name="external_id" type="varchar"/>
            <column name="provider_status" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.citizens" pkName="pk_citizens">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="person_id" type="bigint" notnull="true" references="uam.persons.id" foreignKeyName="fk_citizens_person_id" foreignKeyIndex="ix_citizens_person_id"/>
            <column name="country" type="varchar(2)" notnull="true"/>
            <column name="first_name" type="varchar(100)"/>
            <column name="last_name" type="varchar(250)"/>
            <column name="state" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="address" type="varchar(4000)"/>
            <column name="address2" type="varchar(4000)"/>
            <column name="postal" type="varchar(50)"/>
        </createTable>
        <createTable name="uam.country_operation_policies" pkName="pk_country_operation_policies">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_country_operation_policies_brand_id" foreignKeyIndex="ix_country_operation_policies_brand_id"/>
            <column name="code" type="varchar(2)" notnull="true"/>
            <column name="sweepstake" type="boolean" defaultValue="false" notnull="true"/>
            <column name="precision" type="varchar(7)" notnull="true" checkConstraint="check ( precision in ('country','state'))" checkConstraintName="ck_country_operation_policies_precision"/>
            <column name="blocked" type="varchar[]" notnull="true"/>
            <column name="allowed" type="varchar[]" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_country_operation_policies_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.daily_bonus_templates" pkName="pk_daily_bonus_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_daily_bonus_templates_brand_id" foreignKeyIndex="ix_daily_bonus_templates_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="vip_points" type="decimal(16,2)"/>
            <column name="gold_amount" type="decimal(16,2)"/>
            <column name="sweepstake_amount" type="decimal(16,2)"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_daily_bonus_templates_modified_by_id" foreignKeyIndex="ix_daily_bonus_templates_modified_by_id"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_daily_bonus_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.deactivate_provider_policy" pkName="pk_deactivate_provider_policy">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="provider_code" type="varchar"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_deactivate_provider_policy_brand_id" foreignKeyIndex="ix_deactivate_provider_policy_brand_id"/>
            <column name="exception_classes" type="varchar[]"/>
            <column name="error_codes" type="varchar[]"/>
            <column name="max_failure_percentage" type="integer" notnull="true"/>
            <column name="deactivate_seconds_interval" type="integer" notnull="true"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
        </createTable>
        <createTable name="uam.email_templates" pkName="pk_email_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_email_templates_brand_id" foreignKeyIndex="ix_email_templates_brand_id"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="tags" type="varchar[]"/>
            <column name="template_id" type="varchar(4000)" notnull="true"/>
            <column name="description" type="varchar(4000)"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_email_templates_modified_by_id" foreignKeyIndex="ix_email_templates_modified_by_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_email_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns="brand_id"/>
        </createTable>
        <createTable name="uam.email_verification_request" pkName="pk_email_verification_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="token" type="varchar" notnull="true" unique="uq_email_verification_request_token"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_email_verification_request_account_id" foreignKeyIndex="ix_email_verification_request_account_id"/>
            <column name="verified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="verified_at" type="timestamp"/>
            <column name="expire_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="core.xp_levels" pkName="pk_xp_levels">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="level" type="integer" notnull="true"/>
            <column name="points_to_next_level" type="decimal(16,2)" notnull="true"/>
            <column name="points" type="decimal(16,2)" notnull="true"/>
            <column name="bonus" type="decimal(16,2)" notnull="true"/>
            <column name="vip_points" type="decimal(16,2)" notnull="true"/>
            <column name="multiplier" type="decimal(16,2)"/>
            <column name="reward_ads_multiplier" type="decimal(16,2)"/>
            <column name="reward_campaign_multiplier" type="decimal(16,2)"/>
            <column name="reward_vip_subscription_multiplier" type="decimal(16,2)"/>
            <column name="daily_bonus_multiplier" type="decimal(16,2)"/>
            <column name="levels" type="jsonb"/>
            <column name="inherit_products_from_id" type="bigint" references="core.xp_levels.id" foreignKeyName="fk_xp_levels_inherit_products_from_id" foreignKeyIndex="ix_xp_levels_inherit_products_from_id"/>
            <column name="inherit_offers_from_id" type="bigint" references="core.xp_levels.id" foreignKeyName="fk_xp_levels_inherit_offers_from_id" foreignKeyIndex="ix_xp_levels_inherit_offers_from_id"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_xp_levels_brand_id" foreignKeyIndex="ix_xp_levels_brand_id"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_xp_levels_modified_by_id" foreignKeyIndex="ix_xp_levels_modified_by_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_xp_levels_brand_id_level" columnNames="brand_id,level" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="core.xp_level_products" pkName="pk_xp_level_products">
            <column name="level_id" type="bigint" notnull="true" primaryKey="true"/>
            <column name="product_id" type="integer" notnull="true" primaryKey="true"/>
            <foreignKey name="fk_xp_level_products_xp_levels" columnNames="level_id" refColumnNames="id" refTableName="core.xp_levels" indexName="ix_xp_level_products_xp_levels"/>
            <foreignKey name="fk_xp_level_products_products" columnNames="product_id" refColumnNames="id" refTableName="core.products" indexName="ix_xp_level_products_products"/>
        </createTable>
        <createTable name="uam.fraud_applied_rules" pkName="pk_fraud_applied_rules">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="rule_id" type="varchar" notnull="true"/>
            <column name="score" type="integer" notnull="true"/>
            <column name="fraud_response_id" type="bigint" notnull="true" references="uam.fraud_response.id" foreignKeyName="fk_fraud_applied_rules_fraud_response_id" foreignKeyIndex="ix_fraud_applied_rules_fraud_response_id"/>
            <column name="at" type="date" notnull="true"/>
        </createTable>
        <createTable name="uam.fraud_response" pkName="pk_fraud_response">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="response_id" type="varchar" notnull="true"/>
            <column name="cookie_hash" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="core.products" pkName="pk_products">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="type" type="varchar" notnull="true"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="name" type="varchar"/>
            <column name="route" type="varchar" notnull="true"/>
            <column name="mode" type="varchar(15)" notnull="true" checkConstraint="check ( mode in ('gold','sweepstake','fiat','gold_sweepstake','gold_fiat','free'))" checkConstraintName="ck_products_mode"/>
            <column name="orientation" type="varchar(9)" notnull="true" checkConstraint="check ( orientation in ('landscape','portrait','both'))" checkConstraintName="ck_products_orientation"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="max_lines" type="integer" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_products_brand_id" foreignKeyIndex="ix_products_brand_id"/>
            <column name="provider_id" type="integer" references="core.providers.id" foreignKeyName="fk_products_provider_id" foreignKeyIndex="ix_products_provider_id"/>
            <column name="supplier" type="varchar" notnull="true"/>
            <column name="rank" type="integer" notnull="true"/>
            <column name="volatility" type="integer" notnull="true"/>
            <column name="segment" type="varchar"/>
            <column name="more_info" type="boolean" defaultValue="false" notnull="true"/>
            <column name="mini_mode" type="boolean" defaultValue="false" notnull="true"/>
            <column name="tile_url" type="varchar(4000)"/>
            <column name="tile_large" type="varchar(4000)"/>
            <column name="tile_medium" type="varchar(4000)"/>
            <column name="tile_small" type="varchar(4000)"/>
            <column name="segment_tags" type="varchar[]"/>
            <column name="tags" type="varchar[]"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_products_modified_by_id" foreignKeyIndex="ix_products_modified_by_id"/>
            <column name="admin_only" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_products_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
            <uniqueConstraint name="uq_products_brand_id_route" columnNames="brand_id,route" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.invitation_reward_policies" pkName="pk_invitation_reward_policies">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_invitation_reward_policies_brand_id" foreignKeyIndex="ix_invitation_reward_policies_brand_id"/>
            <column name="action" type="varchar(14)" notnull="true" checkConstraint="check ( action in ('sign_up','first_purchase','purchase','purchase_aggr','first_withdraw','withdraw'))" checkConstraintName="ck_invitation_reward_policies_action"/>
            <column name="gold_amount" type="decimal(16,2)"/>
            <column name="sweepstake_amount" type="decimal(16,2)"/>
            <column name="fiat_amount" type="decimal(16,2)"/>
            <column name="absolute" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_invitation_reward_policies_brand_id_action" columnNames="brand_id,action" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.jackpots" pkName="pk_jackpots">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="winner_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_jackpots_winner_id" foreignKeyIndex="ix_jackpots_winner_id"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_jackpots_brand_id" foreignKeyIndex="ix_jackpots_brand_id"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="initial_amount" type="decimal(16,2)" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="start_at" type="timestamp" notnull="true"/>
            <column name="end_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.jackpot_account_contributions" pkName="pk_jackpot_account_contributions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_jackpot_account_contributions_account_id" foreignKeyIndex="ix_jackpot_account_contributions_account_id"/>
            <column name="jackpot_id" type="bigint" notnull="true" references="uam.jackpots.id" foreignKeyName="fk_jackpot_account_contributions_jackpot_id" foreignKeyIndex="ix_jackpot_account_contributions_jackpot_id"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_jackpot_account_contributions_account_id_jackpot_id_cu_1" columnNames="account_id,jackpot_id,currency" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.jackpot_contributions" pkName="pk_jackpot_contributions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_jackpot_contributions_account_id" foreignKeyIndex="ix_jackpot_contributions_account_id"/>
            <column name="jackpot_id" type="bigint" references="uam.jackpots.id" foreignKeyName="fk_jackpot_contributions_jackpot_id" foreignKeyIndex="ix_jackpot_contributions_jackpot_id"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.jackpot_multipliers" pkName="pk_jackpot_multipliers">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="multiplier" type="decimal(16,2)" notnull="true"/>
            <column name="chance" type="decimal(6,3)" notnull="true"/>
        </createTable>
        <createTable name="uam.jackpot_opt_history" pkName="pk_jackpot_opt_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_jackpot_opt_history_account_id" foreignKeyIndex="ix_jackpot_opt_history_account_id"/>
            <column name="auto" type="boolean" defaultValue="false" notnull="true"/>
            <column name="opt_out_at" type="timestamp"/>
            <column name="opt_in_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.jackpots_personal" pkName="pk_jackpots_personal">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_jackpots_personal_account_id" foreignKeyIndex="ix_jackpots_personal_account_id"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="status" type="varchar(9)" notnull="true" checkConstraint="check ( status in ('active','completed','payed','expired'))" checkConstraintName="ck_jackpots_personal_status"/>
            <column name="multiplier_id" type="bigint" references="uam.jackpot_multipliers.id" foreignKeyName="fk_jackpots_personal_multiplier_id" foreignKeyIndex="ix_jackpots_personal_multiplier_id"/>
            <column name="total_win" type="decimal(16,2)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.kyc_verification_requests" pkName="pk_kyc_verification_requests">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_kyc_verification_requests_account_id" foreignKeyIndex="ix_kyc_verification_requests_account_id"/>
            <column name="status" type="varchar(15)" notnull="true" checkConstraint="check ( status in ('initial','in_review','confirmed','id_confirmed','doc_review','doc_declined','session_expired','declined','blocked'))" checkConstraintName="ck_kyc_verification_requests_status"/>
            <column name="id_first_name" type="varchar"/>
            <column name="id_last_name" type="varchar"/>
            <column name="id_birth_date" type="date"/>
            <column name="doc_status" type="varchar(15)" notnull="true" checkConstraint="check ( doc_status in ('initial','in_review','confirmed','id_confirmed','doc_review','doc_declined','session_expired','declined','blocked'))" checkConstraintName="ck_kyc_verification_requests_doc_status"/>
            <column name="doc_country" type="varchar(2)"/>
            <column name="doc_state" type="varchar"/>
            <column name="doc_city" type="varchar"/>
            <column name="doc_address" type="varchar"/>
            <column name="doc_postal" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_kyc_verification_requests_provider_code" columnNames="provider,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.offer_templates" pkName="pk_offer_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="title" type="varchar"/>
            <column name="type" type="varchar(12)" notnull="true" checkConstraint="check ( type in ('one_time','subscription','permanent'))" checkConstraintName="ck_offer_templates_type"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_offer_templates_brand_id" foreignKeyIndex="ix_offer_templates_brand_id"/>
            <column name="day_of_week" type="varchar[]"/>
            <column name="price" type="decimal(16,2)"/>
            <column name="vip_level" type="integer"/>
            <column name="vip_points" type="decimal(16,2)"/>
            <column name="gold_amount" type="decimal(16,2)"/>
            <column name="gold_first_offer_amount" type="decimal(16,2)"/>
            <column name="sweepstake_amount" type="decimal(16,2)"/>
            <column name="sweepstake_first_offer_amount" type="decimal(16,2)"/>
            <column name="vip_levels" type="varchar[]"/>
            <column name="segment" type="varchar"/>
            <column name="segment_tags" type="varchar[]"/>
            <column name="exclude_segment" type="varchar"/>
            <column name="exclude_segment_tags" type="varchar[]"/>
            <column name="tags" type="varchar[]"/>
            <column name="start_at" type="timestamp"/>
            <column name="end_at" type="timestamp"/>
            <column name="priority" type="integer" notnull="true"/>
            <column name="tc_url" type="varchar(4000)"/>
            <column name="banner_image_url" type="varchar(4000)"/>
            <column name="pop_up_image_url" type="varchar(4000)"/>
            <column name="rules" type="varchar[]"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_offer_templates_modified_by_id" foreignKeyIndex="ix_offer_templates_modified_by_id"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="show_stickybar" type="boolean" defaultValue="false" notnull="true"/>
            <column name="supported_platforms" type="varchar[]" notnull="true"/>
            <column name="supported_platform" type="varchar(7)" notnull="true" checkConstraint="check ( supported_platform in ('web','android','ios'))" checkConstraintName="ck_offer_templates_supported_platform"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_offer_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.password_reset_tokens" pkName="pk_password_reset_tokens">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="token" type="varchar" notnull="true" unique="uq_password_reset_tokens_token"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_password_reset_tokens_account_id" foreignKeyIndex="ix_password_reset_tokens_account_id"/>
            <column name="expire_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.payment_method_fraud_applied_rule" pkName="pk_payment_method_fraud_applied_rule">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="method_id" type="bigint" notnull="true" references="payment.payment_methods.id" foreignKeyName="fk_payment_method_fraud_applied_rule_method_id" foreignKeyIndex="ix_payment_method_fraud_applied_rule_method_id"/>
            <column name="provider_id" type="integer" notnull="true" references="payment.providers.id" foreignKeyName="fk_payment_method_fraud_applied_rule_provider_id" foreignKeyIndex="ix_payment_method_fraud_applied_rule_provider_id"/>
            <column name="rule_code" type="varchar" notnull="true"/>
            <column name="last_seen" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_payment_method_fraud_applied_rule_method_id_provider_i_1" columnNames="method_id,provider_id,rule_code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.payment_method_error" pkName="pk_payment_method_error">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="method_id" type="bigint" notnull="true" references="payment.payment_methods.id" foreignKeyName="fk_payment_method_error_method_id" foreignKeyIndex="ix_payment_method_error_method_id"/>
            <column name="provider_id" type="integer" notnull="true" references="payment.providers.id" foreignKeyName="fk_payment_method_error_provider_id" foreignKeyIndex="ix_payment_method_error_provider_id"/>
            <column name="error_code" type="varchar" notnull="true"/>
            <column name="last_seen" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_payment_method_error_method_id_provider_id_error_code" columnNames="method_id,provider_id,error_code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.payment_orders" pkName="pk_payment_orders">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="transaction_id" type="uuid" notnull="true" unique="uq_payment_orders_transaction_id"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="order_sn" type="varchar" notnull="true" unique="uq_payment_orders_order_sn"/>
            <column name="source_id" type="varchar"/>
            <column name="referer" type="varchar(4000)"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_payment_orders_account_id" foreignKeyIndex="ix_payment_orders_account_id"/>
            <column name="sweepstake" type="boolean" defaultValue="false" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="status" type="varchar"/>
            <column name="reason" type="varchar(4000)"/>
            <column name="fraud_score" type="integer"/>
            <column name="fraud_request_id" type="varchar"/>
            <column name="confirm_score" type="integer"/>
            <column name="fraud_response_id" type="bigint"/>
            <column name="api_error" type="jsonb"/>
            <column name="error" type="jsonb"/>
            <column name="error_code" type="varchar"/>
            <column name="exception_class" type="varchar"/>
            <column name="http_code" type="smallint"/>
            <column name="response_code" type="varchar"/>
            <column name="card_network_error" type="varchar"/>
            <column name="routing_fraud_rules" type="varchar[]"/>
            <column name="message" type="varchar"/>
            <column name="simulation" type="boolean" defaultValue="false" notnull="true"/>
            <column name="secure3d" type="boolean" defaultValue="false" notnull="true"/>
            <column name="success" type="boolean" defaultValue="false" notnull="true"/>
            <column name="platform" type="varchar(7)" notnull="true" checkConstraint="check ( platform in ('web','android','ios'))" checkConstraintName="ck_payment_orders_platform"/>
            <column name="user_agent" type="varchar(4000)" notnull="true"/>
            <column name="remote_ip" type="varchar(45)" notnull="true"/>
            <column name="redirect_url" type="varchar(4000)"/>
            <column name="temp_token" type="varchar(4000)"/>
            <column name="confirm_temp_token" type="varchar(4000)"/>
            <column name="payment_method_id" type="bigint" references="payment.payment_methods.id" foreignKeyName="fk_payment_orders_payment_method_id" foreignKeyIndex="ix_payment_orders_payment_method_id"/>
            <column name="card_transaction_details_id" type="integer" uniqueOneToOne="uq_payment_orders_card_transaction_details_id" references="payment.card_transaction_details.id" foreignKeyName="fk_payment_orders_card_transaction_details_id"/>
            <column name="offer_id" type="bigint" notnull="true" references="payment.offer_templates.id" foreignKeyName="fk_payment_orders_offer_id" foreignKeyIndex="ix_payment_orders_offer_id"/>
            <column name="order_type" type="varchar(7)" notnull="true" checkConstraint="check ( order_type in ('create','cancel','expire','restart'))" checkConstraintName="ck_payment_orders_order_type"/>
            <column name="description" type="varchar"/>
            <column name="refunded" type="boolean" defaultValue="false" notnull="true"/>
            <column name="refunded_at" type="timestamp"/>
            <column name="chargeback_at" type="date"/>
            <column name="chargeback_status" type="varchar(13)" checkConstraint="check ( chargeback_status in ('pending','won','lost','chargeback','alert','rdr','partial_match','unknown'))" checkConstraintName="ck_payment_orders_chargeback_status"/>
            <column name="at" type="date" notnull="true"/>
            <column name="ethoca_id" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_payment_orders_provider_code" columnNames="provider,code" oneToOne="false" nullableColumns=""/>
            <uniqueConstraint name="uq_payment_orders_fraud_request_id" columnNames="fraud_request_id" oneToOne="false" nullableColumns="fraud_request_id"/>
        </createTable>
        <createTable name="uam.persons" pkName="pk_persons">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="first_name" type="varchar(100)"/>
            <column name="last_name" type="varchar(250)"/>
            <column name="birth_date" type="date"/>
            <column name="verified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="gender" type="varchar(6)" checkConstraint="check ( gender in ('male','female'))" checkConstraintName="ck_persons_gender"/>
            <column name="birth_country" type="varchar(2)"/>
            <column name="birth_city" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.phone_number_request" pkName="pk_phone_number_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="phone_number" type="varchar(25)" notnull="true"/>
            <column name="sid" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_phone_number_request_account_id" foreignKeyIndex="ix_phone_number_request_account_id"/>
            <column name="last_attempt" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_phone_number_request_account_id_sid" columnNames="account_id,sid" oneToOne="false" nullableColumns="account_id"/>
        </createTable>
        <createTable name="uam.product_bet_limits" pkName="pk_product_bet_limits">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="product_id" type="integer" notnull="true" references="core.products.id" foreignKeyName="fk_product_bet_limits_product_id" foreignKeyIndex="ix_product_bet_limits_product_id"/>
            <column name="max_win_multiplier" type="integer"/>
            <column name="max_bet_multiplier" type="integer"/>
            <column name="min_bet" type="decimal"/>
            <column name="default_bet" type="decimal(16,2)"/>
            <column name="bets" type="varchar[]"/>
            <column name="bet_levels" type="integer[]"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_product_bet_limits_product_id_currency" columnNames="product_id,currency" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="core.product_categories" pkName="pk_product_categories">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="route" type="varchar"/>
            <column name="icon" type="varchar"/>
            <column name="rank" type="integer" notnull="true"/>
            <column name="lines" type="integer" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_product_categories_brand_id" foreignKeyIndex="ix_product_categories_brand_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_product_categories_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.providers" pkName="pk_providers">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_providers_brand_id" foreignKeyIndex="ix_providers_brand_id"/>
            <column name="type" type="varchar(8)" notnull="true" checkConstraint="check ( type in ('purchase','withdraw'))" checkConstraintName="ck_providers_type"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="sign_up_score_from" type="integer"/>
            <column name="sign_up_score_to" type="integer"/>
            <column name="display_name" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_providers_brand_id_code_type" columnNames="brand_id,code,type" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="core.providers" pkName="pk_providers">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="supports_xp" type="boolean" defaultValue="false" notnull="true"/>
            <column name="name" type="varchar"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_providers_brand_id" foreignKeyIndex="ix_providers_brand_id"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_providers_modified_by_id" foreignKeyIndex="ix_providers_modified_by_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_providers_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.push_templates" pkName="pk_push_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_push_templates_brand_id" foreignKeyIndex="ix_push_templates_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="tags" type="varchar[]"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="body" type="varchar(4000)" notnull="true"/>
            <column name="image_url" type="varchar(4000)"/>
            <column name="priority" type="varchar"/>
            <column name="alert" type="varchar(4000)"/>
            <column name="color" type="varchar"/>
            <column name="sound" type="varchar(4000)"/>
            <column name="icon" type="varchar(4000)"/>
            <column name="badge" type="integer"/>
            <column name="click_action" type="varchar(4000)"/>
            <column name="ttl" type="integer"/>
            <column name="category" type="varchar"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_push_templates_modified_by_id" foreignKeyIndex="ix_push_templates_modified_by_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_push_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns="brand_id"/>
        </createTable>
        <createTable name="uam.push_tokens" pkName="pk_push_tokens">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="token" type="varchar(4000)" notnull="true" unique="uq_push_tokens_token"/>
            <column name="user_agent" type="varchar(4000)"/>
            <column name="at" type="date" notnull="true"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.referrer_templates" pkName="pk_referrer_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_referrer_templates_brand_id" foreignKeyIndex="ix_referrer_templates_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="pattern" type="varchar" notnull="true"/>
            <column name="mode" type="varchar(22)" notnull="true" checkConstraint="check ( mode in ('default','gold','gold_free','sweepstake','sweepstake_preview','sweepstake_preview_web'))" checkConstraintName="ck_referrer_templates_mode"/>
            <column name="vip_points" type="decimal(16,2)"/>
            <column name="gold_money" type="decimal(16,2)"/>
            <column name="sweepstake_money" type="decimal(16,2)"/>
            <column name="fiat_money" type="decimal(16,2)"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_referrer_templates_modified_by_id" foreignKeyIndex="ix_referrer_templates_modified_by_id"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_referrer_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.registration_operation_policies" pkName="pk_registration_operation_policies">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_registration_operation_policies_brand_id" foreignKeyIndex="ix_registration_operation_policies_brand_id"/>
            <column name="code" type="varchar(2)" notnull="true"/>
            <column name="mode" type="varchar(10)" notnull="true" checkConstraint="check ( mode in ('prohibited','full'))" checkConstraintName="ck_registration_operation_policies_mode"/>
            <column name="precision" type="varchar(7)" notnull="true" checkConstraint="check ( precision in ('country','state'))" checkConstraintName="ck_registration_operation_policies_precision"/>
            <column name="blocked" type="varchar[]" notnull="true"/>
            <column name="allowed" type="varchar[]" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_registration_operation_policies_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.reward_campaigns" pkName="pk_reward_campaigns">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_reward_campaigns_brand_id" foreignKeyIndex="ix_reward_campaigns_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="category" type="varchar" notnull="true"/>
            <column name="type" type="varchar" notnull="true"/>
            <column name="objective" type="varchar" notnull="true"/>
            <column name="channel_issued" type="varchar" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_reward_campaigns_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.reward_creditors" pkName="pk_reward_creditors">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true"/>
            <column name="department" type="varchar" notnull="true"/>
            <column name="source_system" type="varchar(10)" notnull="true" checkConstraint="check ( source_system in ('ysi','bloomreach'))" checkConstraintName="ck_reward_creditors_source_system"/>
            <column name="automated_trigger" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_reward_creditors_name" columnNames="name" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.routing_config" pkName="pk_routing_config">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_routing_config_brand_id" foreignKeyIndex="ix_routing_config_brand_id"/>
            <column name="group_id" type="integer" references="payment.routing_config.id" foreignKeyName="fk_routing_config_group_id" foreignKeyIndex="ix_routing_config_group_id"/>
            <column name="weight" type="integer"/>
            <column name="sequence" type="integer" notnull="true"/>
            <column name="type" type="varchar(19)" notnull="true" checkConstraint="check ( type in ('spreedly','spreedly_apple_pay','spreedly_google_pay'))" checkConstraintName="ck_routing_config_type"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_routing_config_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.routing_error" pkName="pk_routing_error">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="error_code" type="varchar" notnull="true"/>
            <column name="description" type="varchar"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_routing_error_brand_id" foreignKeyIndex="ix_routing_error_brand_id"/>
            <column name="type" type="varchar(15)" notnull="true" checkConstraint="check ( type in ('card_network','seon_rule','provider_custom'))" checkConstraintName="ck_routing_error_type"/>
            <column name="routing_config_code" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_routing_error_brand_id_error_code_routing_config_code__1" columnNames="brand_id,error_code,routing_config_code,type" oneToOne="false" nullableColumns="brand_id,routing_config_code"/>
        </createTable>
        <createTable name="payment.routing_member_card_token" pkName="pk_routing_member_card_token">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="provider_id" type="integer" notnull="true" references="payment.providers.id" foreignKeyName="fk_routing_member_card_token_provider_id" foreignKeyIndex="ix_routing_member_card_token_provider_id"/>
            <column name="payment_method_id" type="bigint" notnull="true" references="payment.payment_methods.id" foreignKeyName="fk_routing_member_card_token_payment_method_id" foreignKeyIndex="ix_routing_member_card_token_payment_method_id"/>
            <column name="token" type="varchar" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_routing_member_card_token_provider_id_payment_method_id" columnNames="provider_id,payment_method_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.routing_provider_card_token" pkName="pk_routing_provider_card_token">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="provider_id" type="integer" notnull="true" references="payment.providers.id" foreignKeyName="fk_routing_provider_card_token_provider_id" foreignKeyIndex="ix_routing_provider_card_token_provider_id"/>
            <column name="card_payment_method_id" type="integer" notnull="true" references="payment.card_payment_method.id" foreignKeyName="fk_routing_provider_card_token_card_payment_method_id" foreignKeyIndex="ix_routing_provider_card_token_card_payment_method_id"/>
            <column name="token" type="varchar" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_routing_provider_card_token_provider_id_card_payment_m_1" columnNames="provider_id,card_payment_method_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.temporary_tokens" partitionMode="YEAR" partitionColumn="at" pkName="pk_temporary_tokens">
            <column name="id" type="bigint" notnull="true" primaryKey="true"/>
            <column name="at" type="date" notnull="true" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_temporary_tokens_account_id" foreignKeyIndex="ix_temporary_tokens_account_id"/>
            <column name="temp_token" type="varchar(4000)" notnull="true"/>
            <column name="permanent_token" type="varchar(4000)" notnull="true"/>
            <column name="used" type="boolean" defaultValue="false" notnull="true"/>
            <column name="game_mode" type="varchar(7)" checkConstraint="check ( game_mode in ('default','mini'))" checkConstraintName="ck_temporary_tokens_game_mode"/>
            <column name="expire_at" type="timestamp"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_temporary_tokens_temp_token_at" columnNames="temp_token,at" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.temporary_tokens_seq" identityType="sequence" identityIncrement="1" sequenceName="uam.temporary_tokens_id_seq" pkName="pk_temporary_tokens_seq">
            <column name="id" type="bigint" primaryKey="true" identity="true"/>
            <column name="start_interval" type="bigint" notnull="true"/>
            <column name="end_interval" type="bigint" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.utm_templates" pkName="pk_utm_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="campaign" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_utm_templates_brand_id" foreignKeyIndex="ix_utm_templates_brand_id"/>
            <uniqueConstraint name="uq_utm_templates_brand_id_campaign" columnNames="brand_id,campaign" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.user_agent_infos" pkName="pk_user_agent_infos">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="user_agent" type="varchar(4000)" notnull="true" unique="uq_user_agent_infos_user_agent"/>
            <column name="agent_build" type="varchar"/>
            <column name="agent_class" type="varchar"/>
            <column name="agent_information_email" type="varchar"/>
            <column name="agent_information_url" type="varchar"/>
            <column name="agent_language" type="varchar"/>
            <column name="agent_language_code" type="varchar"/>
            <column name="agent_name" type="varchar"/>
            <column name="agent_name_version" type="varchar"/>
            <column name="agent_name_version_major" type="varchar"/>
            <column name="agent_security" type="varchar"/>
            <column name="agent_uuid" type="varchar"/>
            <column name="agent_version" type="varchar"/>
            <column name="agent_version_major" type="varchar"/>
            <column name="anonymized" type="varchar"/>
            <column name="carrier" type="varchar"/>
            <column name="device_brand" type="varchar"/>
            <column name="device_class" type="varchar"/>
            <column name="device_cpu" type="varchar"/>
            <column name="device_cpu_bits" type="varchar"/>
            <column name="device_firmware_version" type="varchar"/>
            <column name="device_name" type="varchar"/>
            <column name="device_version" type="varchar"/>
            <column name="facebook_carrier" type="varchar"/>
            <column name="facebook_device_class" type="varchar"/>
            <column name="facebook_device_name" type="varchar"/>
            <column name="facebook_device_version" type="varchar"/>
            <column name="facebook_fbop" type="varchar"/>
            <column name="facebook_fbss" type="varchar"/>
            <column name="facebook_operating_system_name" type="varchar"/>
            <column name="facebook_operating_system_version" type="varchar"/>
            <column name="gsa_installation_id" type="varchar"/>
            <column name="hacker_attack_vector" type="varchar"/>
            <column name="hacker_toolkit" type="varchar"/>
            <column name="ie_compatibility_name_version" type="varchar"/>
            <column name="ie_compatibility_name_version_major" type="varchar"/>
            <column name="ie_compatibility_version" type="varchar"/>
            <column name="ie_compatibility_version_major" type="varchar"/>
            <column name="kobo_affiliate" type="varchar"/>
            <column name="kobo_platform_id" type="varchar"/>
            <column name="layout_engine_build" type="varchar"/>
            <column name="layout_engine_class" type="varchar"/>
            <column name="layout_engine_name" type="varchar"/>
            <column name="layout_engine_name_version" type="varchar"/>
            <column name="layout_engine_name_version_major" type="varchar"/>
            <column name="layout_engine_version" type="varchar"/>
            <column name="layout_engine_version_major" type="varchar"/>
            <column name="network_type" type="varchar"/>
            <column name="operating_system_class" type="varchar"/>
            <column name="operating_system_name" type="varchar"/>
            <column name="operating_system_name_version" type="varchar"/>
            <column name="operating_system_name_version_major" type="varchar"/>
            <column name="operating_system_version" type="varchar"/>
            <column name="operating_system_version_build" type="varchar"/>
            <column name="operating_system_version_major" type="varchar"/>
            <column name="webview_app_name" type="varchar"/>
            <column name="webview_app_name_version" type="varchar"/>
            <column name="webview_app_name_version_major" type="varchar"/>
            <column name="webview_app_version" type="varchar"/>
            <column name="webview_app_version_major" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.user_agent_templates" pkName="pk_user_agent_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_user_agent_templates_brand_id" foreignKeyIndex="ix_user_agent_templates_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="rank" type="integer" notnull="true"/>
            <column name="expression" type="varchar" notnull="true"/>
            <column name="mode" type="varchar(22)" notnull="true" checkConstraint="check ( mode in ('default','gold','gold_free','sweepstake','sweepstake_preview','sweepstake_preview_web'))" checkConstraintName="ck_user_agent_templates_mode"/>
            <column name="vip_points" type="decimal(16,2)"/>
            <column name="gold_money" type="decimal(16,2)"/>
            <column name="sweepstake_money" type="decimal(16,2)"/>
            <column name="fiat_money" type="decimal(16,2)"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_user_agent_templates_modified_by_id" foreignKeyIndex="ix_user_agent_templates_modified_by_id"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_user_agent_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="core.vip_levels" pkName="pk_vip_levels">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="level" type="integer" notnull="true"/>
            <column name="points_to_next_level" type="decimal(16,2)" notnull="true"/>
            <column name="points" type="decimal(16,2)" notnull="true"/>
            <column name="name" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_vip_levels_brand_id" foreignKeyIndex="ix_vip_levels_brand_id"/>
            <column name="multiplier" type="decimal(16,2)"/>
            <column name="level_up_multiplier" type="decimal(16,2)"/>
            <column name="reward_ads_multiplier" type="decimal(16,2)"/>
            <column name="reward_vip_subscription_multiplier" type="decimal(16,2)"/>
            <column name="vip_support" type="boolean" defaultValue="false" notnull="true"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_vip_levels_modified_by_id" foreignKeyIndex="ix_vip_levels_modified_by_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_vip_levels_brand_id_level" columnNames="brand_id,level" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="core.vip_level_products" pkName="pk_vip_level_products">
            <column name="level_id" type="bigint" notnull="true" primaryKey="true"/>
            <column name="product_id" type="integer" notnull="true" primaryKey="true"/>
            <foreignKey name="fk_vip_level_products_vip_levels" columnNames="level_id" refColumnNames="id" refTableName="core.vip_levels" indexName="ix_vip_level_products_vip_levels"/>
            <foreignKey name="fk_vip_level_products_products" columnNames="product_id" refColumnNames="id" refTableName="core.products" indexName="ix_vip_level_products_products"/>
        </createTable>
        <createTable name="payment.vip_level_purchase_templates" pkName="pk_vip_level_purchase_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_vip_level_purchase_templates_brand_id" foreignKeyIndex="ix_vip_level_purchase_templates_brand_id"/>
            <column name="from_total" type="decimal(16,2)"/>
            <column name="to_total" type="decimal(16,2)"/>
            <column name="first_day_total" type="decimal(16,2)"/>
            <column name="first_week_total" type="decimal(16,2)"/>
            <column name="first2_weeks_total" type="decimal(16,2)"/>
            <column name="first10_total" type="decimal(16,2)"/>
            <column name="modified_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_vip_level_purchase_templates_modified_by_id" foreignKeyIndex="ix_vip_level_purchase_templates_modified_by_id"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_vip_level_purchase_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.visitors_meta" pkName="pk_visitors_meta">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="token" type="varchar(4000)" notnull="true" unique="uq_visitors_meta_token"/>
            <column name="browser_name" type="varchar"/>
            <column name="browser_version" type="varchar"/>
            <column name="browser_major" type="varchar"/>
            <column name="os" type="varchar"/>
            <column name="os_version" type="varchar"/>
            <column name="device" type="varchar"/>
            <column name="user_agent" type="varchar(4000)"/>
            <column name="country" type="varchar(2)"/>
            <column name="state" type="varchar"/>
            <column name="city" type="varchar(4000)"/>
            <column name="ip" type="varchar(45)" notnull="true"/>
            <column name="referrer" type="varchar(4000)"/>
            <column name="incognito" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.wallet_sessions" partitionMode="DAY" partitionColumn="at" pkName="pk_wallet_sessions">
            <column name="id" type="bigint" notnull="true" primaryKey="true"/>
            <column name="at" type="date" notnull="true" primaryKey="true"/>
            <column name="source" type="varchar" notnull="true"/>
            <column name="session_id" type="varchar(36)" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_wallet_sessions_account_id" foreignKeyIndex="ix_wallet_sessions_account_id"/>
            <column name="transactions" type="jsonb"/>
            <column name="profit" type="decimal(16,2)" notnull="true"/>
            <column name="loss" type="decimal(16,2)" notnull="true"/>
            <column name="product_id" type="integer" references="core.products.id" foreignKeyName="fk_wallet_sessions_product_id" foreignKeyIndex="ix_wallet_sessions_product_id"/>
            <column name="gold_level_id" type="bigint" references="core.xp_levels.id" foreignKeyName="fk_wallet_sessions_gold_level_id" foreignKeyIndex="ix_wallet_sessions_gold_level_id"/>
            <column name="balance" type="decimal(16,2)"/>
            <column name="unplayed_balance" type="decimal(16,2)"/>
            <column name="redeemable_balance" type="decimal(16,2)"/>
            <column name="pending_withdraw" type="decimal(16,2)"/>
            <column name="type" type="varchar(17)" notnull="true" checkConstraint="check ( type in ('payment','withdraw','lotto','bingo','game','mini_game','internal','clean_up','gold_mine_jackpot','my_stash_jackpot','jackpot','unknown'))" checkConstraintName="ck_wallet_sessions_type"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_wallet_sessions_source_session_id_currency_at" columnNames="source,session_id,currency,at" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.wallet_sessions_seq" identityType="sequence" identityIncrement="1" sequenceName="uam.wallet_sessions_id_seq" pkName="pk_wallet_sessions_seq">
            <column name="id" type="bigint" primaryKey="true" identity="true"/>
            <column name="start_interval" type="bigint" notnull="true"/>
            <column name="end_interval" type="bigint" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.withdraw_money_requests" pkName="pk_withdraw_money_requests">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="transaction_id" type="uuid" notnull="true" unique="uq_withdraw_money_requests_transaction_id"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_withdraw_money_requests_account_id" foreignKeyIndex="ix_withdraw_money_requests_account_id"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="ext_code" type="varchar"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="email" type="varchar(4000)"/>
            <column name="status" type="varchar(14)" notnull="true" checkConstraint="check ( status in ('failed','pre_authorized','confirmed','declined','cancelled','locked'))" checkConstraintName="ck_withdraw_money_requests_status"/>
            <column name="uam_status" type="varchar(14)" checkConstraint="check ( uam_status in ('failed','pre_authorized','confirmed','declined','cancelled','locked'))" checkConstraintName="ck_withdraw_money_requests_uam_status"/>
            <column name="fraud_score" type="integer"/>
            <column name="confirm_score" type="integer"/>
            <column name="decline_score" type="integer"/>
            <column name="fraud_response_id" type="bigint"/>
            <column name="error" type="jsonb"/>
            <column name="method_id" type="bigint" references="payment.withdraw_methods.id" foreignKeyName="fk_withdraw_money_requests_method_id" foreignKeyIndex="ix_withdraw_money_requests_method_id"/>
            <column name="payout_type" type="varchar"/>
            <column name="at" type="date" notnull="true"/>
            <column name="locked_at" type="timestamp"/>
            <column name="locked_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_withdraw_money_requests_locked_by_id" foreignKeyIndex="ix_withdraw_money_requests_locked_by_id"/>
            <column name="confirmed_at" type="timestamp"/>
            <column name="confirmed_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_withdraw_money_requests_confirmed_by_id" foreignKeyIndex="ix_withdraw_money_requests_confirmed_by_id"/>
            <column name="declined_at" type="timestamp"/>
            <column name="declined_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_withdraw_money_requests_declined_by_id" foreignKeyIndex="ix_withdraw_money_requests_declined_by_id"/>
            <column name="decline_reason" type="varchar(10)" checkConstraint="check ( decline_reason in ('refund','forfeit','error','player_req','other'))" checkConstraintName="ck_withdraw_money_requests_decline_reason"/>
            <column name="pre_confirmed_at" type="timestamp"/>
            <column name="cancelled_at" type="timestamp"/>
            <column name="confirmation_reason" type="varchar(5)" checkConstraint="check ( confirmation_reason in ('nsc','other'))" checkConstraintName="ck_withdraw_money_requests_confirmation_reason"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_withdraw_money_requests_provider_code" columnNames="provider,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createIndex indexName="ix_accounts_hash" tableName="uam.accounts" columns="hash"/>
        <createIndex indexName="ix_accounts_bot" tableName="uam.accounts" columns="bot"/>
        <createIndex indexName="ix_accounts_deleted" tableName="uam.accounts" columns="deleted"/>
        <createIndex indexName="ix_accounts_at" tableName="uam.accounts" columns="at"/>
        <createIndex indexName="ix_accounts_normalized_email" tableName="uam.accounts" columns="normalized_email"/>
        <createIndex indexName="ix_accounts_admin" tableName="uam.accounts" columns="admin"/>
        <createIndex indexName="ix_accounts_guest" tableName="uam.accounts" columns="guest" definition="create index ix_accounts_guest on uam.accounts using hash(guest)" platforms="POSTGRES"/>
        <createIndex indexName="ix_account_daily_bonuses_at" tableName="uam.account_daily_bonuses" columns="at"/>
        <createIndex indexName="ix_account_daily_bonuses_next_bonus_at" tableName="uam.account_daily_bonuses" columns="next_bonus_at"/>
        <createIndex indexName="ix_account_daily_bonuses_next_bonus_day" tableName="uam.account_daily_bonuses" columns="next_bonus_day"/>
        <createIndex indexName="ix_account_daily_bonuses_last_accept" tableName="uam.account_daily_bonuses" columns="last_accept"/>
        <createIndex indexName="ix_account_meta_info_last_sign_in" tableName="uam.account_meta_info" columns="last_sign_in"/>
        <createIndex indexName="ix_account_meta_info_last_sign_in_ip" tableName="uam.account_meta_info" columns="last_sign_in_ip"/>
        <createIndex indexName="ix_account_meta_info_sign_up" tableName="uam.account_meta_info" columns="sign_up"/>
        <createIndex indexName="ix_account_meta_info_sign_up_ip" tableName="uam.account_meta_info" columns="sign_up_ip"/>
        <createIndex indexName="ix_payment_methods_account_id_remember" tableName="payment.payment_methods" columns="account_id,remember"/>
        <createIndex indexName="ix_payment_methods_code" tableName="payment.payment_methods" columns="code"/>
        <createIndex indexName="ix_advertising_campaign_info_ip" tableName="uam.advertising_campaign_info" columns="ip"/>
        <createIndex indexName="ix_bonus_rewards_at" tableName="uam.bonus_rewards" columns="at"/>
        <createIndex indexName="ix_kyc_verification_requests_scan_reference" tableName="uam.kyc_verification_requests" columns="scan_reference"/>
        <createIndex indexName="ix_kyc_verification_requests_at" tableName="uam.kyc_verification_requests" columns="at"/>
        <createIndex indexName="ix_payment_orders_account_id_success" tableName="payment.payment_orders" columns="account_id,success"/>
        <createIndex indexName="ix_payment_orders_source_id" tableName="payment.payment_orders" columns="source_id"/>
        <createIndex indexName="ix_payment_orders_refunded" tableName="payment.payment_orders" columns="refunded"/>
        <createIndex indexName="ix_payment_orders_chargeback_status" tableName="payment.payment_orders" columns="chargeback_status"/>
        <createIndex indexName="ix_payment_orders_at" tableName="payment.payment_orders" columns="at"/>
        <createIndex indexName="ix_phone_number_request_phone_number" tableName="uam.phone_number_request" columns="phone_number"/>
        <createIndex indexName="ix_push_tokens_at" tableName="uam.push_tokens" columns="at"/>
        <createIndex indexName="ix_temporary_tokens_permanent_token_at" tableName="uam.temporary_tokens" columns="permanent_token,at"/>
        <createIndex indexName="ix_visitors_meta_ip" tableName="uam.visitors_meta" columns="ip"/>
        <createIndex indexName="ix_withdraw_money_requests_at" tableName="payment.withdraw_money_requests" columns="at"/>
    </changeSet>
</migration>