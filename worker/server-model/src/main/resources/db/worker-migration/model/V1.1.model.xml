<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createSchema name="fraud"/>
        <alterColumn columnName="id" tableName="uam.account_fraud_info" dropForeignKey="fk_account_fraud_info_id"/>
        <addColumn tableName="uam.account_fraud_info">
            <column name="account_id" type="bigint" uniqueOneToOne="uq_account_fraud_info_account_id" references="uam.accounts.id" foreignKeyName="fk_account_fraud_info_account_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.account_invitation_dropdowns">
            <column name="rewarded_lvl_1" type="boolean" defaultValue="false" notnull="true"/>
            <column name="rewarded_lvl_2" type="boolean" defaultValue="false" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.account_invitation_info">
            <column name="invited_lvl_1" type="integer" notnull="true"/>
            <column name="invited_lvl_2" type="integer" notnull="true"/>
        </addColumn>
        <createTable name="uam.account_jackpot_preferences" pkName="pk_account_jackpot_preferences">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="currency" type="varchar" notnull="true"/>
            <column name="opt_in" type="boolean" defaultValue="false" notnull="true"/>
            <column name="auto_opted_out" type="boolean" defaultValue="false" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_account_jackpot_preferences_account_id" foreignKeyIndex="ix_account_jackpot_preferences_account_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_jackpot_preferences_account_id_currency" columnNames="account_id,currency" oneToOne="false" nullableColumns="account_id"/>
        </createTable>
        <alterColumn columnName="type" tableName="payment.payment_methods" currentType="varchar" notnull="true" currentNotnull="false"/>
        <addColumn tableName="uam.account_postal_codes">
            <column name="at" type="date" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.account_preferences">
            <column name="do_not_send_sms" type="boolean" defaultValue="false" notnull="true"/>
        </addColumn>
        <alterColumn columnName="reason" tableName="payment.account_purchase_limit" type="varchar(22)" currentType="varchar(20)" currentNotnull="false" checkConstraint="check ( reason in ('member_request','suspicious_behaviour','verification_needed','chb_mitigation','verification_needed_pm'))" checkConstraintName="ck_account_purchase_limit_reason"/>
        <alterColumn columnName="type" tableName="payment.withdraw_methods" checkConstraint="check ( type in ('ach','sci','pay_pal','skrill','skrill_ach','nuvei','nuvei_mazooma_ach','masspay_ach','prizeout','pay_with_my_bank'))" checkConstraintName="ck_withdraw_methods_type"/>
        <createTable name="fraud.aml_check" withHistory="true" pkName="pk_aml_check">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true"/>
            <column name="crime_list_match" type="boolean" defaultValue="false" notnull="true"/>
            <column name="pep_match" type="boolean" defaultValue="false" notnull="true"/>
            <column name="watchlist_match" type="boolean" defaultValue="false" notnull="true"/>
            <column name="sanction_match" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_aml_check_account_id" columnNames="account_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <addColumn tableName="payment.deactivate_provider_policy">
            <column name="http_error_codes" type="integer[]"/>
        </addColumn>
        <alterColumn columnName="points_to_next_level" tableName="core.xp_levels" type="decimal(20,2)" currentType="decimal(16,2)" currentNotnull="true"/>
        <alterColumn columnName="points" tableName="core.xp_levels" type="decimal(20,2)" currentType="decimal(16,2)" currentNotnull="true"/>
        <addColumn tableName="uam.fraud_applied_rules">
            <column name="operation" type="varchar"/>
        </addColumn>
        <createTable name="fraud.fraud_decline_rules" pkName="pk_fraud_decline_rules">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="rule_id" type="varchar" notnull="true"/>
            <column name="decline_reason" type="varchar" notnull="true"/>
            <column name="decline_code" type="varchar(25)" notnull="true" checkConstraint="check ( decline_code in ('err_ok','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_tc_required','err_sr_required','err_pp_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','unrecognized'))" checkConstraintName="ck_fraud_decline_rules_decline_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <addUniqueConstraint constraintName="uq_fraud_response_response_id" tableName="uam.fraud_response" columnNames="response_id" oneToOne="false" nullableColumns=""/>
        <createTable name="fraud.fraud_transaction_labels" pkName="pk_fraud_transaction_labels">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="fraud_request_id" type="varchar" notnull="true"/>
            <column name="label" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="fraud.idology_check" withHistory="true" pkName="pk_idology_check">
            <column name="account_id" type="bigint" primaryKey="true"/>
            <column name="restriction_key" type="varchar"/>
            <column name="restriction_message" type="varchar"/>
            <column name="id_number" type="bigint"/>
            <column name="failed" type="varchar(500)"/>
            <column name="error" type="varchar(500)"/>
            <column name="at" type="timestamp" notnull="true"/>
        </createTable>
        <addColumn tableName="core.products">
            <column name="launch_date" type="timestamp" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.invitation_reward_policies">
            <column name="level" type="integer" notnull="true"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_invitation_reward_policies_brand_id_action" tableName="uam.invitation_reward_policies" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_invitation_reward_policies_brand_id_action_level" tableName="uam.invitation_reward_policies" columnNames="brand_id,action,level" oneToOne="false" nullableColumns=""/>
        <alterColumn columnName="amount" tableName="uam.jackpots" type="decimal(21,7)" currentType="decimal(16,2)" currentNotnull="true"/>
        <alterColumn columnName="start_at" tableName="uam.jackpots" currentType="timestamp" notnull="false" currentNotnull="true"/>
        <alterColumn columnName="end_at" tableName="uam.jackpots" currentType="timestamp" notnull="false" currentNotnull="true"/>
        <addColumn tableName="uam.jackpots">
            <column name="template_id" type="bigint" references="uam.jackpot_templates.id" foreignKeyName="fk_jackpots_template_id" foreignKeyIndex="ix_jackpots_template_id"/>
        </addColumn>
        <alterColumn columnName="amount" tableName="uam.jackpot_account_contributions" type="decimal(21,7)" currentType="decimal(16,2)" currentNotnull="true"/>
        <alterColumn columnName="chance" tableName="uam.jackpot_multipliers" type="decimal(10,7)" currentType="decimal(6,3)" currentNotnull="true"/>
        <addColumn tableName="uam.jackpot_multipliers">
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.jackpot_opt_history">
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <alterColumn columnName="amount" tableName="uam.jackpots_personal" type="decimal(21,7)" currentType="decimal(16,2)" currentNotnull="true"/>
        <alterColumn columnName="total_win" tableName="uam.jackpots_personal" type="decimal(21,7)" currentType="decimal(16,2)" currentNotnull="true"/>
        <createTable name="uam.jackpot_preferences" pkName="pk_jackpot_preferences">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_jackpot_preferences_brand_id" foreignKeyIndex="ix_jackpot_preferences_brand_id"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="optional" type="boolean" defaultValue="false" notnull="true"/>
            <column name="chance" type="decimal(10,7)"/>
            <column name="duration" type="bigint" notnull="true"/>
            <column name="min_contribution" type="decimal(16,2)" notnull="true"/>
            <column name="max_contribution" type="decimal(16,2)"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_jackpot_preferences_brand_id_currency" columnNames="brand_id,currency" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.jackpot_templates" pkName="pk_jackpot_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="preferences_id" type="bigint" notnull="true" references="uam.jackpot_preferences.id" foreignKeyName="fk_jackpot_templates_preferences_id" foreignKeyIndex="ix_jackpot_templates_preferences_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="title" type="varchar"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="type" type="varchar(9)" notnull="true" checkConstraint="check ( type in ('gold_mine','my_stash','mini','minor','major','grand'))" checkConstraintName="ck_jackpot_templates_type"/>
            <column name="chance" type="decimal(10,7)"/>
            <column name="seed" type="decimal(16,2)"/>
            <column name="contribution_stake" type="decimal(10,7)" notnull="true"/>
            <column name="next_contribution_stake" type="decimal(10,7)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.legal_rules" pkName="pk_legal_rules">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_legal_rules_brand_id" foreignKeyIndex="ix_legal_rules_brand_id"/>
            <column name="rule_version" type="integer" notnull="true"/>
            <column name="setting" type="varchar(7)" notnull="true" checkConstraint="check ( setting in ('Tickbox','Popup','Nothing'))" checkConstraintName="ck_legal_rules_setting"/>
            <column name="text" type="varchar"/>
            <column name="version_date" type="date" notnull="true"/>
            <column name="type" type="varchar(2)" notnull="true" checkConstraint="check ( type in ('tc','sr','pp'))" checkConstraintName="ck_legal_rules_type"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_legal_rules_brand_id_type_rule_version" columnNames="brand_id,type,rule_version" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="fraud.idology_patriot_act" pkName="pk_idology_patriot_act">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="list" type="varchar"/>
            <column name="score" type="integer"/>
            <column name="check_account_id" type="bigint" references="fraud.idology_check.account_id" foreignKeyName="fk_idology_patriot_act_check_account_id" foreignKeyIndex="ix_idology_patriot_act_check_account_id"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <addColumn tableName="payment.payment_orders">
            <column name="remote_ip_country" type="varchar"/>
            <column name="remote_ip_state" type="varchar"/>
            <column name="ethoca_alert_type" type="varchar(21)" checkConstraint="check ( ethoca_alert_type in ('customerdispute_alert','issuer_alert'))" checkConstraintName="ck_payment_orders_ethoca_alert_type"/>
            <column name="fraud_reported_at" type="date"/>
            <column name="fraud_report_reason_code" type="varchar"/>
        </addColumn>
        <addColumn tableName="core.product_categories">
            <column name="style" type="varchar(7)" notnull="true" checkConstraint="check ( style in ('small','large','heading','default'))" checkConstraintName="ck_product_categories_style"/>
            <column name="type" type="varchar(9)" notnull="true" checkConstraint="check ( type in ('jackpot','providers','default'))" checkConstraintName="ck_product_categories_type"/>
        </addColumn>
        <createIndex indexName="ix_account_meta_info_last_activity" tableName="uam.account_meta_info" columns="last_activity"/>
        <createIndex indexName="uq_account_saved_method_code_type" tableName="payment.payment_methods" columns="" unique="true" definition="CREATE UNIQUE INDEX uq_account_saved_method_code_type ON payment.payment_methods (account_id, remember, code, type) WHERE remember = true" platforms="POSTGRES"/>
        <createIndex indexName="ix_account_postal_codes_account_id_at" tableName="uam.account_postal_codes" columns="account_id,at"/>
        <createIndex indexName="ix_fraud_transaction_labels_fraud_request_id" tableName="fraud.fraud_transaction_labels" columns="fraud_request_id"/>
        <createIndex indexName="ix_idology_check_at" tableName="fraud.idology_check" columns="at"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="first_deposit" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_deposit" tableName="uam.account_meta_info"/>
        <dropColumn columnName="deposit_count" tableName="uam.account_meta_info"/>
        <dropColumn columnName="total_deposit_amount" tableName="uam.account_meta_info"/>
        <dropColumn columnName="method" tableName="payment.payment_methods"/>
        <dropColumn columnName="deleted" tableName="payment.payment_methods"/>
        <dropColumn columnName="error_codes" tableName="payment.deactivate_provider_policy"/>
        <dropColumn columnName="confirm_score" tableName="payment.payment_orders"/>
        <dropColumn columnName="error" tableName="payment.payment_orders"/>
    </changeSet>
</migration>