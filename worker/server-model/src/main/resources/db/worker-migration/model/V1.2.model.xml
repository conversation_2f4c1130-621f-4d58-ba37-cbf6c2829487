<!--<?xml version="1.0" encoding="UTF-8" standalone="yes"?>-->
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="uam.postback_filters" pkName="pk_postback_filters">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_postback_filters_account_id" foreignKeyIndex="ix_account_free_spins_account_id"/>
            <column name="postback_type" type="varchar(255)" notnull="true" checkConstraint="check ( tag in ('appsflyer','bankrolls','bloomreach','box','clickhouse','converttologic','facebook','flows','graphyte','intelitics','moonshoot','pinterest','prodege','rapid-client','sendgrid','sidelines','snapchat','tiktok','voluum','xe-rate'))" checkConstraintName="ck_account_tags_tag"/>
            <column name="event_name" type="varchar(255)" notnull="true"/>
            <column name="jsonpath_query" type="varchar(4000)"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_postback_filters_account_id_postback_type_event_name" columnNames="account_id, postback_type, event_name" oneToOne="false" nullableColumns=""/>
        </createTable>
    </changeSet>
</migration>