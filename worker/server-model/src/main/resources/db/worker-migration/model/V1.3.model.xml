<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="worker.clean_up_job_summary" pkName="pk_clean_up_job_summary">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="started_at" type="timestamp" notnull="true"/>
            <column name="ended_at" type="timestamp" notnull="true"/>
            <column name="total_users_processed" type="bigint" notnull="true"/>
            <column name="users_sent_first_warning" type="bigint" notnull="true"/>
            <column name="users_sent_second_warning" type="bigint" notnull="true"/>
            <column name="users_reactivated_after_first_warning" type="bigint" notnull="true"/>
            <column name="users_reactivated_after_second_warning" type="bigint" notnull="true"/>
            <column name="users_cleaned_up" type="bigint" notnull="true"/>
            <column name="total_gc_expired" type="decimal" notnull="true"/>
            <column name="total_sc_expired" type="decimal" notnull="true"/>
            <column name="ok" type="boolean" notnull="true"/>
        </createTable>
        <createIndex indexName="ix_clean_up_job_summary_ended_at" tableName="worker.clean_up_job_summary" columns="ended_at"/>
    </changeSet>
</migration>