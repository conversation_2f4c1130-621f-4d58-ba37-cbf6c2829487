package framework.listeners;

import org.springframework.test.context.TestContext;
import org.springframework.test.context.TestExecutionListener;

import com.turbospaces.ebean.EbeanJpaManager;

import api.v1.ForcementModeSpec;
import api.v1.OrientationModeSpec;
import api.v1.ProductModeSpec;
import api.v1.ProviderSpec;
import gamehub.model.ImmutableBrand;
import gamehub.model.ImmutableProvider;
import gamehub.model.Product;
import gamehub.model.ProductSupplier;
import io.ebean.Transaction;
import lombok.SneakyThrows;
import uam.model.Brand;
import uam.model.InternalProductSpec;
import uam.model.PasswordPolicy;
import uam.model.Provider;
import worker.WorkerEbeanJpaManager;
import worker.WorkerMasterEbeanJpaManager;
import worker.utils.UamDataGenerator;

public class WorkerBrandDataGenerator implements TestExecutionListener {

    public static final String BRAND = "bluedream";

    public static Brand genBrand(EbeanJpaManager ebean, String brandName, Transaction tx) {
        return genBrand(ebean, brandName, "USD", "G", tx);
    }

    public static Brand genBrand(EbeanJpaManager ebean, String brandName, String fiatCurrency, String goldCurrency, Transaction tx) {
        Brand brand = new Brand(brandName);
        brand.setName(brandName);
        brand.setTitle(brandName);
        brand.setFiatCurrency(fiatCurrency);
        brand.setSweepstakeCurrency("SC");
        brand.setGoldCurrency(goldCurrency);
        PasswordPolicy passwordPolicy = new PasswordPolicy();
        passwordPolicy.setShouldNotMatchEmail(false);
        brand.setPasswordPolicy(passwordPolicy);
        ebean.save(brand, tx);

        UamDataGenerator.genVipLevels(brand, ebean, tx);
        UamDataGenerator.genXpLevels(brand, ebean, tx);

        return brand;
    }

    @Override
    @SneakyThrows
    public void beforeTestMethod(TestContext testContext) {
        WorkerEbeanJpaManager ebean = testContext.getApplicationContext().getBean(WorkerMasterEbeanJpaManager.class);
        try (Transaction tx = ebean.newTransaction()) {
            Brand brand = UamDataGenerator.createBrand(
                    BRAND,
                    ForcementModeSpec.SWEEPSTAKE,
                    null,
                    null);
            ebean.save(brand, tx);

            UamDataGenerator.genVipLevels(brand, ebean, tx);
            UamDataGenerator.genXpLevels(brand, ebean, tx);

            Provider provider = new Provider(brand, ProviderSpec.INTERNAL_LOTTO.code());
            brand.addProvider(provider);
            ebean.save(provider, tx);

            ProductSupplier supplier = new ProductSupplier();
            supplier.setProvider(ebean.find(ImmutableProvider.class, provider.getId()));
            supplier.setCode(provider.getCode());
            supplier.setTitle(provider.getCode());
            supplier.setRank(0);
            supplier.setEnabled(true);
            supplier.setSearchCode(provider.getCode());
            supplier.setIcon(provider.getCode().concat(".svg"));
            supplier.setNew(false);
            supplier.setShowInSideMenu(true);
            ebean.save(supplier, tx);

            Product product = new Product(ebean.find(ImmutableBrand.class, brand.getId()), InternalProductSpec.CASHBACK.code());
            product.setMode(ProductModeSpec.GOLD_SWEEPSTAKE);
            product.setType("internal");
            product.setProvider(ebean.find(ImmutableProvider.class, provider.getId()));
            product.setSupplierName(supplier.getTitle());
            product.setSupplier(supplier);
            product.setTitle(InternalProductSpec.CASHBACK.code());
            product.setOrientation(OrientationModeSpec.BOTH);
            ebean.save(product, tx);

            ebean.save(brand, tx);

            tx.commit();
        }
    }
}
