package framework.listeners;

import java.math.BigDecimal;

import org.springframework.test.context.TestContext;
import org.springframework.test.context.TestExecutionListener;

import io.ebean.Transaction;
import lombok.SneakyThrows;
import payment.model.OfferTemplate;
import payment.model.immutable.ImmutableBrand;
import payment.type.OfferPlatformSpec;
import payment.type.OfferTypeSpec;
import payment.type.WelcomeOfferTypeSpec;
import worker.WorkerEbeanJpaManager;
import worker.WorkerJpaManager;
import worker.WorkerMasterEbeanJpaManager;

public class WorkerOfferDataGenerator implements TestExecutionListener {

    private static final String OFFER_3000_SC = "offer_3000_sc";
    private static final String OFFER_100_DOLLARS = "offer_100_d";
    private static final String OFFER_9999_DOLLARS = "offer_9999_d";

    public static OfferTemplate getOffer(WorkerJpaManager ebean, Transaction tx) {
        return ebean.find(OfferTemplate.class)
                .usingTransaction(tx)
                .where()
                .eq("code", OFFER_3000_SC)
                .findOne();
    }

    public static ImmutableBrand getBrand(WorkerJpaManager ebean, Transaction tx) {
        return ebean.find(ImmutableBrand.class)
                .usingTransaction(tx)
                .where()
                .eq("name", WorkerBrandDataGenerator.BRAND)
                .findOne();
    }

    @Override
    @SneakyThrows
    public void beforeTestMethod(TestContext testContext) {
        WorkerEbeanJpaManager ebean = testContext.getApplicationContext().getBean(WorkerMasterEbeanJpaManager.class);
        try (Transaction tx = ebean.newTransaction()) {
            ImmutableBrand brand = getBrand(ebean, tx);

            OfferTemplate template = new OfferTemplate(brand, OFFER_3000_SC);
            template.setTitle(OFFER_3000_SC);
            template.setPrice(BigDecimal.valueOf(3));
            template.setSweepstakeAmount(BigDecimal.valueOf(3));
            template.setGoldAmount(BigDecimal.valueOf(3000L));
            template.setVipPoints(BigDecimal.valueOf(30L));
            template.setType(OfferTypeSpec.PERMANENT);
            template.setPriority(50);
            template.setSupportedPlatform(OfferPlatformSpec.WEB);
            template.setWelcomeOfferType(WelcomeOfferTypeSpec.NONE);
            ebean.save(template, tx);

            OfferTemplate template2 = new OfferTemplate(brand, OFFER_100_DOLLARS);
            template2.setTitle(OFFER_100_DOLLARS);
            template2.setPrice(BigDecimal.valueOf(100));
            template2.setSweepstakeAmount(BigDecimal.valueOf(3));
            template2.setGoldAmount(BigDecimal.valueOf(3000L));
            template2.setVipPoints(BigDecimal.valueOf(30L));
            template2.setType(OfferTypeSpec.PERMANENT);
            template2.setPriority(50);
            template2.setShowStickybar(true);
            template2.setSupportedPlatform(OfferPlatformSpec.WEB);
            template2.setWelcomeOfferType(WelcomeOfferTypeSpec.NONE);
            ebean.save(template2, tx);

            OfferTemplate template3 = new OfferTemplate(brand, OFFER_9999_DOLLARS);
            template3.setTitle(OFFER_9999_DOLLARS);
            template3.setPrice(BigDecimal.valueOf(9999));
            template3.setSweepstakeAmount(BigDecimal.valueOf(3));
            template3.setGoldAmount(BigDecimal.valueOf(3000L));
            template3.setVipPoints(BigDecimal.valueOf(30L));
            template3.setType(OfferTypeSpec.PERMANENT);
            template3.setPriority(50);
            template3.setShowStickybar(true);
            template3.setSupportedPlatform(OfferPlatformSpec.WEB);
            template3.setWelcomeOfferType(WelcomeOfferTypeSpec.NONE);
            ebean.save(template3, tx);

            tx.commit();
        }
    }
}
