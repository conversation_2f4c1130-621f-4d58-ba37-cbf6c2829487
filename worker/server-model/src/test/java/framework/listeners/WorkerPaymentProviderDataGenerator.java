package framework.listeners;

import java.util.Optional;

import org.springframework.test.context.TestContext;
import org.springframework.test.context.TestExecutionListener;

import io.ebean.Transaction;
import lombok.SneakyThrows;
import payment.model.Provider;
import payment.model.ProviderIntegrationTypeSpec;
import payment.model.ProviderTypeSpec;
import payment.model.immutable.ImmutableBrand;
import payment.type.PurchaseProviderSpec;
import uam.api.v1.PaymentProvider;
import worker.WorkerJpaManager;
import worker.WorkerMasterEbeanJpaManager;

public class WorkerPaymentProviderDataGenerator implements TestExecutionListener {

    public static Provider getPaymentProvider(WorkerJpaManager ebean, PaymentProvider paymentProvider, Transaction tx) {
        Provider provider = ebean.find(Provider.class)
                .usingTransaction(tx)
                .where()
                .eq("code", paymentProvider.name().toLowerCase())
                .and()
                .eq("type", ProviderTypeSpec.PURCHASE.code())
                .findOne();
        return Optional
                .ofNullable(provider)
                .orElseThrow(() -> new IllegalStateException("Can't find Provider with code [" + paymentProvider.name().toLowerCase() + "]"));
    }

    public static void genPurchaseProvider(
            WorkerJpaManager ebean,
            PurchaseProviderSpec paymentProvider,
            ProviderIntegrationTypeSpec integrationType,
            Transaction tx) {
        Provider provider = Provider.builder()
                .code(paymentProvider.getPaymentProviderServerApi().name().toLowerCase())
                .brand(getBrand(ebean, tx))
                .type(ProviderTypeSpec.PURCHASE)
                .integrationType(integrationType)
                .inactive(false)
                .paymentMode(paymentProvider.getPaymentMode())
                .description("some description")
                .displayName(paymentProvider.name())
                .billingDescriptor("Bluedream LTD")
                .build();
        ebean.save(provider);
    }

    @Override
    @SneakyThrows
    public void beforeTestMethod(TestContext testContext) {
        WorkerJpaManager ebean = testContext.getApplicationContext().getBean(WorkerMasterEbeanJpaManager.class);
        try (Transaction tx = ebean.newTransaction()) {
            genPurchaseProvider(ebean, PurchaseProviderSpec.SPREEDLY, ProviderIntegrationTypeSpec.SPREEDLY, tx);
            genPurchaseProvider(ebean, PurchaseProviderSpec.NUVEI_MAZOOMA_ACH, ProviderIntegrationTypeSpec.NUVEI_MAZOOMA_ACH, tx);
            genPurchaseProvider(ebean, PurchaseProviderSpec.FISERV, ProviderIntegrationTypeSpec.FISERV, tx);
            genPurchaseProvider(ebean, PurchaseProviderSpec.SKRILL, ProviderIntegrationTypeSpec.SKRILL, tx);
            genPurchaseProvider(ebean, PurchaseProviderSpec.TRUSTLY, ProviderIntegrationTypeSpec.TRUSTLY, tx);
            tx.commit();
        }
    }

    @SneakyThrows
    private static ImmutableBrand getBrand(WorkerJpaManager ebean, Transaction tx) {
        return ebean.paymentBrandRepo().requiredBrandByName(WorkerBrandDataGenerator.BRAND, tx);
    }
}
