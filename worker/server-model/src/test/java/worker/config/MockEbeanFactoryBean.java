package worker.config;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.AbstractEbeanFactoryBean;
import com.turbospaces.ebean.EbeanDatabaseConfig;

import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import worker.WorkerEbeanJpaManager;
import worker.WorkerMasterEbeanJpaManager;

public class MockEbeanFactoryBean extends AbstractEbeanFactoryBean<WorkerMasterEbeanJpaManager> {

    public MockEbeanFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
        super(props, meterRegistry, tracer, config);
    }

    @Override
    public Class<?> getObjectType() {
        return WorkerMasterEbeanJpaManager.class;
    }

    @Override
    protected WorkerEbeanJpaManager createEbean(SpiEbeanServer db) {
        return new WorkerMasterEbeanJpaManager(props, meterRegistry, tracer, db, timer, true);
    }
}
