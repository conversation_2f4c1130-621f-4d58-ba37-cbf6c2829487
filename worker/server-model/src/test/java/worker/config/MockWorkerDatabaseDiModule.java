package worker.config;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.flywaydb.core.api.output.MigrateResult;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.PostgresqlServiceInfo;
import org.springframework.cloud.service.common.RelationalServiceInfo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.testcontainers.containers.PostgreSQLContainer;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import com.turbospaces.ebean.FlywayUberRunner;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import com.turbospaces.ups.UPSs;

import ebean.DefaultEncryptKeyManager;
import ebean.OpenPGPEncryptor;
import io.ebean.config.EncryptKeyManager;
import io.ebean.platform.postgres.PostgresPlatform;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import model.Schemas;
import worker.WorkerEbeanJpaManager;
import worker.WorkerMasterEbeanJpaManager;

@Slf4j
@Configuration
public class MockWorkerDatabaseDiModule {
    public static final String[] SCHEMAS = { Schemas.CORE, Schemas.UAM, Schemas.PAYMENT, Schemas.FRAUD, Schemas.GAME_HUB, Schemas.WORKER };

    private static final String DB_USERNAME = "app_owner";
    private static final String DB_PASSWORD = "app_owner";
    private static final String DB_NAME = "defaultdb";

    @SuppressWarnings("resource")
    @Bean(initMethod = "start", destroyMethod = "stop")
    public PostgreSQLContainer<?> postgreSQLContainer() {
        return new PostgreSQLContainer<>("postgres:14")
                .withDatabaseName(DB_NAME)
                .withUsername(DB_USERNAME)
                .withPassword(DB_PASSWORD)
                .withInitScript("db/init.sql")
                .withReuse(true);
    }

    @Bean
    public HikariDataSourceFactoryBean datasource(PostgreSQLContainer<?> postgreSQLContainer, ApplicationProperties props, MeterRegistry meterRegistry) {
        String jdbcUrl = postgreSQLContainer.getJdbcUrl();
        String uri = UPSs.toUriInfo(UPSs.POSTGRES_OWNER,
                PostgresqlServiceInfo.POSTGRES_SCHEME,
                postgreSQLContainer.getHost(),
                postgreSQLContainer.getFirstMappedPort(),
                postgreSQLContainer.getUsername(),
                postgreSQLContainer.getPassword(),
                postgreSQLContainer.getDatabaseName()).getUriString();
        RelationalServiceInfo appUps = new PostgresqlServiceInfo("postgres", uri, jdbcUrl);

        return new HikariDataSourceFactoryBean(props, meterRegistry, appUps);
    }

    @Bean
    public EncryptKeyManager encryptKeyManager(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry) {
        return new DefaultEncryptKeyManager(props, cloud, meterRegistry);
    }

    @Bean
    public EbeanDatabaseConfig ebeanConfig(HikariDataSourceFactoryBean ds, ApplicationProperties props, EncryptKeyManager encryptKeyManager) throws Exception {
        var config = new EbeanDatabaseConfig(ds.getObject(), props);
        config.addAll(new TestWorkerEntities());
        config.setDatabasePlatform(new PostgresPlatform());
        config.setEncryptKeyManager(encryptKeyManager);
        config.setEncryptor(new OpenPGPEncryptor());

        return config;
    }

    @Bean
    public MockEbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
        return new MockEbeanFactoryBean(props, meterRegistry, tracer, config) {
            @NonNull
            @Override
            protected WorkerMasterEbeanJpaManager createInstance() throws Exception {
                WorkerMasterEbeanJpaManager ebean = super.createInstance();
                MigrateResult migrateResult = FlywayUberRunner.run(ebean, SCHEMAS);
                log.info(ReflectionToStringBuilder.toString(migrateResult, ToStringStyle.SHORT_PREFIX_STYLE));

                return ebean;
            }
        };
    }
}
