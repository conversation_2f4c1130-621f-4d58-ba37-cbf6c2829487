package worker.config;

import gamehub.GameHubEntities;
import model.CoreEntities;
import payment.PaymentEntities;
import uam.UamEntities;
import worker.model.ImmutableAccountGameplayInfo;
import worker.model.PostbackFilter;

public class TestWorkerEntities extends CoreEntities {

    public TestWorkerEntities() {
        super();
        add(ImmutableAccountGameplayInfo.class);
        addAll(new PaymentEntities());
        addAll(new UamEntities());
        addAll(new GameHubEntities());
        add(PostbackFilter.class);
    }
}
