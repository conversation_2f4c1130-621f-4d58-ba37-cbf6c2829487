package worker.config;

import com.turbospaces.boot.test.AbstractSpringBootTestContextBootstrapper;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;

public class WorkerSpringBootTestContextBootstrapper extends AbstractSpringBootTestContextBootstrapper<ApplicationProperties> {

    @Override
    protected ApplicationProperties createProps() {
        ApplicationConfig cfg = ApplicationConfig.mock();
        return new ApplicationProperties(cfg.factory());
    }
}
