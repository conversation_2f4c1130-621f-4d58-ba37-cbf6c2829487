package worker.model;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SourceParamTest {
    private static final String SOURCE_PARAM_STRING = "emptyVar1=|var1=val1|_rdt_uuid=UUID_VALUE|emptyVar2=|_rdt_cid=CLICK_ID|";

    @Test
    void shouldCreateSourceParam() {
        SourceParam sourceParam = SourceParam.from(SOURCE_PARAM_STRING);

        assertTrue(sourceParam.getParam("var1").isPresent());
        assertEquals("val1", sourceParam.getParam("var1").orElse(null));
        assertTrue(sourceParam.getParam("_rdt_cid").isPresent());
        assertEquals("CLICK_ID", sourceParam.getParam("_rdt_cid").orElse(null));
        assertTrue(sourceParam.getParam("emptyVar1").isEmpty());
        assertNull(sourceParam.getParam("emptyVar1").orElse(null));
        assertTrue(sourceParam.getParam("emptyVar2").isEmpty());
        assertNull(sourceParam.getParam("emptyVar2").orElse(null));
    }

    @Test
    void shouldCreateSourceParamWithNull() {
        SourceParam sourceParam = SourceParam.from(null);

        assertTrue(sourceParam.getParam("xxx").isEmpty());
        assertTrue(sourceParam.getParam("yyy").isEmpty());
    }

    @Test
    void shouldCreateSourceParamWithEmpty() {
        SourceParam sourceParam = SourceParam.from("");

        assertTrue(sourceParam.getParam("xxx").isEmpty());
        assertTrue(sourceParam.getParam("yyy").isEmpty());
    }
}