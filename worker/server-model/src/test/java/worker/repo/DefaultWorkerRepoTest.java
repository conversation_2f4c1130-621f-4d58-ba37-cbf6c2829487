package worker.repo;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.NullSource;

import io.ebean.Transaction;
import payment.model.PaymentOrder;
import payment.model.WithdrawMoneyRequest;
import uam.model.Account;
import uam.model.AccountBalance;
import uam.model.BonusReward;
import uam.model.Brand;
import uam.model.CleanUpStatusSpec;
import uam.model.RuleTypeSpec;
import worker.model.AccountInfo;
import worker.model.AccountInfoAndBalancesAndBrandId;
import worker.model.AccountRoutingInfo;
import worker.utils.DataGenerationService;
import worker.utils.UamDataGenerator;

class DefaultWorkerRepoTest extends RepoTest {

    private static final String SC = "SC";
    private static final String GC = "GC";

    private WorkerRepo target;

    @BeforeEach
    void init() {
        target = ebean.workerRepo();
    }

    @Test
    void findAccountWithBalances_shouldReturnAccountById_whenOneExists() throws Throwable {
        Account expected;
        try (Transaction tx = ebean.newTransaction()) {
            expected = genAccount(tx);
            tx.commit();
        }

        Optional<Account> actual;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            actual = target.findAccountWithBalances(expected.getId(), tx);
        }

        assertTrue(actual.isPresent());

        Account account = actual.get();
        assertEquals(expected, account);
    }

    @Test
    void findAccountWithBalances_shouldReturnEmptyResult_whenNoAccountExists() throws Throwable {
        Optional<Account> actual;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            actual = target.findAccountWithBalances(1L, tx);
        }

        assertTrue(actual.isEmpty());
    }

    @Test
    void findAccountRoutingList() throws Throwable {
        Account expected;
        try (Transaction tx = ebean.newTransaction()) {
            expected = genAccount(tx);
            genAccount(tx);
            tx.commit();
        }

        List<AccountRoutingInfo> actual;
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            actual = target.findAccountRoutingList(Set.of(expected.getId()), tx);
        }

        assertEquals(1, actual.size());
        AccountRoutingInfo routing = actual.getFirst();
        assertEquals(expected.getId(), routing.id());
        assertEquals(expected.getHash(), routing.hash());
        assertEquals(expected.getBrand().getId(), routing.brandId());
    }

    @Test
    void findAccountsWithBonusRewards() throws Throwable {
        Instant now = Instant.now();
        Instant from = now.minus(1, ChronoUnit.DAYS);
        Instant to = now.plus(1, ChronoUnit.DAYS);

        Account account1, account2, account3, account4;
        try (Transaction tx = ebean.newTransaction()) {
            account1 = genAccount(tx);
            account2 = genAccount(tx);
            genAccount(tx);

            account3 = genAccount(tx);
            account4 = genAccount(tx);

            genBonusReward(account1, now, tx);
            genBonusReward(account3, from.minusMillis(1), tx);
            genBonusReward(account4, to.plusMillis(1), tx);

            tx.commit();
        }

        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var accountIds = target.findAccountsWithBonusRewards(Set.of(account1.getId(), account2.getId(), account3.getId(), account4.getId()), Date.from(from), Date.from(to), tx);
            assertEquals(1, accountIds.size());
            assertTrue(accountIds.contains(account1.getId()));
        }
    }

    @Test
    void findAccountsWithPaymentOrders() throws Throwable {
        Instant now = Instant.now();
        Instant from = now.minus(1, ChronoUnit.DAYS);
        Instant to = now.plus(1, ChronoUnit.DAYS);

        Account account1, account2, account3;
        try (Transaction tx = ebean.newTransaction()) {
            account1 = genAccount(tx);
            account2 = genAccount(tx);
            account3 = genAccount(tx);
            genAccount(tx);

            genOrder(account1, tx);
            genOrder(account1, tx);
            genOrder(account3, from.minusMillis(1), tx);
            genOrder(account3, to.plusMillis(1), tx);

            tx.commit();
        }

        try (var tx = ebean.newReadOnlyTransaction()) {
            var accountIds = target.findAccountsWithPaymentOrders(Set.of(account1.getId(), account2.getId()), Date.from(from), Date.from(to), tx);
            assertEquals(1, accountIds.size());
            assertTrue(accountIds.contains(account1.getId()));
        }
    }

    @Test
    void findAccountsWithWithdraws() throws Throwable {
        Instant now = Instant.now();
        Instant from = now.minus(1, ChronoUnit.DAYS);
        Instant to = now.plus(1, ChronoUnit.DAYS);

        Account account1, account2, account3;
        try (Transaction tx = ebean.newTransaction()) {
            account1 = genAccount(tx);
            account2 = genAccount(tx);
            account3 = genAccount(tx);
            genAccount(tx);

            genWithdraw(account1, tx);
            genWithdraw(account1, tx);
            genWithdraw(account3, from.minusMillis(1), tx);
            genWithdraw(account3, to.plusMillis(1), tx);

            tx.commit();
        }

        try (var tx = ebean.newReadOnlyTransaction()) {
            var accountIds = target.findAccountsWithWithdraws(Set.of(account1.getId(), account2.getId()), Date.from(from), Date.from(to), tx);
            assertEquals(1, accountIds.size());
            assertTrue(accountIds.contains(account1.getId()));
        }
    }

    @EnumSource(CleanUpStatusSpec.class)
    @ParameterizedTest
    void accountsWhoEnteredCleanUpFlowButResumedGameplay(CleanUpStatusSpec status) throws Throwable {
        Instant now = Instant.now();
        Instant from = now.minus(1, ChronoUnit.DAYS);
        Instant to = now.plus(1, ChronoUnit.DAYS);

        Account account1, account2, account3, account4, account5;
        try (var tx = ebean.newTransaction()) {
            account1 = genAccount(tx);
            modifyLastGameplay(account1, now);
            modifyCleanUpStatus(account1, status);

            account2 = genAccount(tx);
            modifyLastGameplay(account2, now);

            account3 = genAccount(tx);
            modifyLastGameplay(account3, from.minusMillis(1));

            account4 = genAccount(tx);
            modifyLastGameplay(account4, to.plusMillis(1));

            account5 = genAccount(tx);
            modifyLastGameplay(account5, now.plusMillis(1));
            modifyCleanUpStatus(account5, status);

            tx.commit();
        }

        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var accounts = target.accountsWhoEnteredCleanUpFlowButResumedGameplay(Date.from(from), Date.from(to), 1, tx);
            assertEquals(1, accounts.size());
            AccountInfo expected = AccountInfo.forGameplay(account1.getId(), status, Date.from(now), null);
            assertTrue(accounts.contains(expected));
        }
    }

    @Test
    void accountsWhoWereCleanedUpButResumedGameplay() throws Throwable {
        Instant now = Instant.now();

        Account account1, account2, account4, account5, account6;
        try (var tx = ebean.newTransaction()) {
            account1 = genAccount(tx);
            modifyCleanUpStatus(account1, CleanUpStatusSpec.FIRST_EMAIL_NOTIFY);

            account2 = genAccount(tx);
            modifyCleanUpStatus(account2, CleanUpStatusSpec.SECOND_EMAIL_NOTIFY);

            genAccount(tx);

            account4 = genAccountWithoutBalance(tx);
            modifyCleanUpStatus(account4, CleanUpStatusSpec.CLEAN_UP_ACCOUNT);

            account5 = genAccount(tx);
            modifyCleanUpStatus(account5, CleanUpStatusSpec.CLEAN_UP_ACCOUNT);

            account6 = genAccount(tx);
            modifyCleanUpStatus(account6, CleanUpStatusSpec.CLEAN_UP_ACCOUNT);
            modifyLastGameplay(account6, now);
            modifyCreatedAt(account6, now);

            tx.commit();
        }

        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var accounts = target.accountsWhoWereCleanedUpButResumedGameplay(1, account5.getId(), tx);
            assertEquals(1, accounts.size());

            AccountInfo accountInfo = new AccountInfo(account6.getId(), CleanUpStatusSpec.CLEAN_UP_ACCOUNT, Date.from(now), Date.from(now), null);
            var balances = List.of(new worker.model.AccountBalance(SC, BigDecimal.TEN), new worker.model.AccountBalance(GC, BigDecimal.TEN));
            AccountInfoAndBalancesAndBrandId expected = new AccountInfoAndBalancesAndBrandId(accountInfo, balances, account6.getBrand().getId());

            assertThat(accounts)
                    .singleElement()
                    .usingRecursiveComparison()
                    .withComparatorForType(BigDecimal::compareTo, BigDecimal.class)
                    .isEqualTo(expected);
        }
    }

    @Test
    void findAcceptedRulesForAccountsBatchByRuleType() throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            var account = genAccount(tx);
            var rules = target.findAcceptedRulesForAccountsBatchByRuleType(List.of(account.getId()), RuleTypeSpec.PP, tx);
            assertEquals(1, rules.size());
        }
    }

    @ParameterizedTest
    @NullSource
    @EnumSource(value = CleanUpStatusSpec.class, names = "CLEAN_UP_ACCOUNT", mode = EnumSource.Mode.EXCLUDE)
    void findAllInactiveAccountsByLastGameplay(CleanUpStatusSpec status) throws Throwable {
        Instant now = Instant.now();
        Instant from = now.minus(1, ChronoUnit.DAYS);
        Instant to = now.plus(1, ChronoUnit.DAYS);

        Account account1, account2, account3, account4, account5;
        try (var tx = ebean.newTransaction()) {
            account1 = genAccount(tx);
            modifyLastGameplay(account1, now);
            modifyCleanUpStatus(account1, status);

            account2 = genAccount(tx);
            modifyLastGameplay(account2, now);
            modifyCleanUpStatus(account2, CleanUpStatusSpec.CLEAN_UP_ACCOUNT);

            account3 = genAccount(tx);
            modifyLastGameplay(account3, from.minusMillis(1));
            modifyCleanUpStatus(account3, status);

            account4 = genAccount(tx);
            modifyLastGameplay(account4, to.plusMillis(1));
            modifyCleanUpStatus(account4, status);

            account5 = genAccount(tx);
            modifyLastGameplay(account5, now.plusMillis(1));
            modifyCleanUpStatus(account5, status);

            tx.commit();
        }

        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var accounts = target.findAllInactiveAccountsByLastGameplay(Date.from(from), Date.from(to), 1, tx);
            assertEquals(1, accounts.size());
            AccountInfo expected = new AccountInfo(account1.getId(), status, Date.from(now), null, null);
            assertTrue(accounts.contains(expected));
        }
    }

    @ParameterizedTest
    @NullSource
    @EnumSource(value = CleanUpStatusSpec.class, names = "CLEAN_UP_ACCOUNT", mode = EnumSource.Mode.EXCLUDE)
    void findAllInactiveAccountsByCreatedAt(CleanUpStatusSpec status) throws Throwable {
        Instant now = Instant.now();
        Instant from = now.minus(1, ChronoUnit.DAYS);
        Instant to = now.plus(1, ChronoUnit.DAYS);

        Account account0, account1, account2, account3, account4, account5, account6;
        try (var tx = ebean.newTransaction()) {
            account0 = genAccount(tx);
            modifyCreatedAt(account0, now);
            modifyCleanUpStatus(account0, status);

            account1 = genAccount(tx);
            modifyCreatedAt(account1, now);
            modifyCleanUpStatus(account1, status);

            account2 = genAccount(tx);
            modifyCleanUpStatus(account2, CleanUpStatusSpec.CLEAN_UP_ACCOUNT);

            account3 = genAccount(tx);
            modifyCreatedAt(account3, from.minusMillis(1));
            modifyCleanUpStatus(account3, status);

            account4 = genAccount(tx);
            modifyCreatedAt(account4, to.plusMillis(1));
            modifyCleanUpStatus(account4, status);

            account5 = genAccount(tx);
            modifyLastGameplay(account5, now);
            modifyCleanUpStatus(account5, status);

            account6 = genAccount(tx);
            modifyLastGameplay(account6, now.plusMillis(1));
            modifyCleanUpStatus(account6, status);

            tx.commit();
        }

        try (var tx = ebean.newReadOnlyTransaction()) {
            var accounts = target.findAllInactiveAccountsByCreatedAt(Date.from(from), Date.from(to), 1, account0.getId(), tx);

            assertEquals(1, accounts.size());
            AccountInfo expected = new AccountInfo(account1.getId(), status, null, Date.from(now), null);
            assertTrue(accounts.contains(expected));
        }
    }

    private Account genAccountWithoutBalance(Transaction tx) throws Throwable {
        Brand brand = getBrand(tx);
        return UamDataGenerator.genAccount(ebean, brand, tx);
    }

    private Account genAccount(Transaction tx) throws Throwable {
        Brand brand = getBrand(tx);
        Account account = UamDataGenerator.genAccount(ebean, brand, tx);
        AccountBalance sc = createBalance(account, SC);
        AccountBalance gc = createBalance(account, GC);

        ebean.saveAll(List.of(sc, gc), tx);
        ebean.save(account, tx);

        return account;
    }

    private static AccountBalance createBalance(Account account, String currency) {
        AccountBalance balance = account.accountBalance(currency);
        balance.setAmount(BigDecimal.TEN);

        return balance;
    }

    private void modifyLastGameplay(Account account, Instant lastGameplay) {
        modifyAccount(account, acc -> {
            acc.getGameplayInfo().setLastGameplay(Date.from(lastGameplay));
            return acc.getGameplayInfo();
        });
    }

    private void modifyCleanUpStatus(Account account, CleanUpStatusSpec status) {
        modifyAccount(account, acc -> {
            acc.getMetaInfo().setCleanUpStatus(status);
            return acc.getMetaInfo();
        });
    }

    private void modifyCreatedAt(Account account, Instant createdAt) {
        modifyAccount(account, acc -> {
            acc.setCreatedAt(Date.from(createdAt));
            return null;
        });
    }

    private void modifyAccount(Account account, Function<Account, Object> modifier) {
        Object toSave = modifier.apply(account);
        if (toSave != null) {
            ebean.save(toSave);
        }
        ebean.save(account);
    }

    private void genBonusReward(Account account, Instant createdAt, Transaction tx) throws Throwable {
        BonusReward reward = UamDataGenerator.genBonusReward(ebean, account, tx);
        reward.setCreatedAt(Date.from(createdAt));
        ebean.save(reward, tx);
    }

    private void genOrder(Account account, Transaction tx) throws Throwable {
        DataGenerationService.genSuccessfulOrder(ebean, account, tx);
    }

    private void genOrder(Account account, Instant createdAt, Transaction tx) throws Throwable {
        PaymentOrder order = DataGenerationService.genSuccessfulOrder(ebean, account, tx);
        order.setCreatedAt(Date.from(createdAt));
        ebean.save(order, tx);
    }

    private void genWithdraw(Account account, Transaction tx) throws Throwable {
        DataGenerationService.genSuccessfulWithdraw(ebean, account, tx);
    }

    private void genWithdraw(Account account, Instant createdAt, Transaction tx) throws Throwable {
        WithdrawMoneyRequest withdraw = DataGenerationService.genSuccessfulWithdraw(ebean, account, tx);
        withdraw.setCreatedAt(Date.from(createdAt));
        ebean.save(withdraw, tx);
    }
}
