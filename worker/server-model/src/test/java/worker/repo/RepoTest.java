package worker.repo;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import framework.listeners.WorkerBrandDataGenerator;
import framework.listeners.WorkerOfferDataGenerator;
import framework.listeners.WorkerPaymentProviderDataGenerator;
import framework.listeners.WorkerWithdrawProviderDataGenerator;
import io.ebean.Transaction;
import uam.model.Account;
import uam.model.BonusReward;
import uam.model.Brand;
import worker.WorkerMasterEbeanJpaManager;
import worker.config.MockWorkerDatabaseDiModule;
import worker.config.WorkerSpringBootTestContextBootstrapper;
import worker.utils.UamDataGenerator;

@ExtendWith(SpringExtension.class)
@BootstrapWith(WorkerSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = MockWorkerDatabaseDiModule.class)
@TestExecutionListeners(
        mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS,
        listeners = {WorkerBrandDataGenerator.class, WorkerPaymentProviderDataGenerator.class, WorkerOfferDataGenerator.class, WorkerWithdrawProviderDataGenerator.class}
)
public abstract class RepoTest {

    protected static final String EMAIL = "<EMAIL>";
    protected static final String TEST_BRAND = "bluedream";

    @Autowired
    protected WorkerMasterEbeanJpaManager ebean;

    @AfterEach
    void cleanup() {
        UamDataGenerator.cleanupEntitiesTables(ebean, Account.class, BonusReward.class, Brand.class);
    }

    protected Brand getBrand(Transaction tx) throws Throwable {
        return ebean.uamBrandRepo().requiredBrandByName(TEST_BRAND, tx);
    }

    protected Account genAccount(Brand brand, Transaction tx) throws Throwable {
        return UamDataGenerator.genAccount(ebean, brand, tx);
    }
}
