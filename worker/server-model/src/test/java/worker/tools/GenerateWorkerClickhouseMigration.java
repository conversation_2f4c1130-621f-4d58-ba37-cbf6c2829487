package worker.tools;

import com.clickhouse.jdbc.DataSourceV1;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import io.ebean.config.DatabaseConfig;
import io.ebean.config.EncryptKey;
import io.ebean.config.EncryptKeyManager;
import io.ebean.platform.clickhouse.ClickHousePlatform;
import io.ebeaninternal.dbmigration.DefaultDbMigration;
import worker.clickhouse.CrmWorkerClickhouseEntities;

@SuppressWarnings("deprecation")
public class GenerateWorkerClickhouseMigration {
    public static void main(String... args) throws Throwable {
        HikariConfig hikari = new HikariConfig();
        hikari.setDataSource(new DataSourceV1("jdbc:ch://localhost:8123/defaultdb?compress_algorithm=zstd"));
        hikari.setUsername("app_owner");
        hikari.setPassword("app_owner");

        try (var db = new HikariDataSource(hikari)) {
            DatabaseConfig sc = new DatabaseConfig();
            sc.setDataSource(db);
            sc.setEncryptKeyManager(new EncryptKeyManager() {
                @Override
                public EncryptKey getEncryptKey(String tableName, String columnName) {
                    return new EncryptKey() {
                        @Override
                        public String getStringValue() {
                            throw new UnsupportedOperationException();
                        }
                    };
                }
            });

            new CrmWorkerClickhouseEntities().forEach(sc::addClass);
            sc.loadModuleInfo(false);

            DefaultDbMigration migration = new DefaultDbMigration();
            migration.setStrictMode(false);
            migration.setServer(sc.build());
            migration.setApplyPrefix("V");
            migration.setMigrationPath("db/crm-worker-clickhouse-migration");
            migration.setPlatform(new ClickHousePlatform());
            migration.setIncludeBuiltInPartitioning(false);
            migration.generateMigration();
        }
    }
}
