package worker.tools;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.ebean.GeneratePostgresMigration;

import worker.WorkerEntities;
import worker.WorkerModelProperties;

public class GenerateWorkerPostgresMigration {
    public static void main(String... args) throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        WorkerModelProperties props = new WorkerModelProperties(cfg.factory());

        cfg.loadLocalDevProperties();
        cfg.setDefaultProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/worker-migration");

        GeneratePostgresMigration.generate(new WorkerEntities(), props);
    }
}
