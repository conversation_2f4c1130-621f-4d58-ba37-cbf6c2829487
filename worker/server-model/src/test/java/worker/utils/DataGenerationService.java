package worker.utils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

import com.turbospaces.common.PlatformUtil;

import api.v1.PlatformSpec;
import framework.listeners.WorkerOfferDataGenerator;
import framework.listeners.WorkerPaymentProviderDataGenerator;
import framework.listeners.WorkerWithdrawProviderDataGenerator;
import io.ebean.Transaction;
import lombok.experimental.UtilityClass;
import payment.PaymentOrderSeq;
import payment.model.AccountWithdrawMethod;
import payment.model.OfferTemplate;
import payment.model.PaymentOrder;
import payment.model.Provider;
import payment.model.RedeemStatusSpec;
import payment.model.WithdrawMethodSpec;
import payment.model.WithdrawMoneyRequest;
import payment.model.immutable.ImmutableAccount;
import uam.api.v1.PaymentProvider;
import uam.model.Account;
import worker.WorkerJpaManager;

@UtilityClass
public class DataGenerationService {

    public static PaymentOrder genSuccessfulOrder(
            WorkerJpaManager ebean,
            Account account,
            OfferTemplate offerTemplate,
            PaymentProvider provider,
            Transaction tx) throws Throwable {
        return genOrder(
                ebean,
                provider,
                account,
                offerTemplate,
                PlatformSpec.WEB,
                PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID(),
                true,
                tx);
    }

    public static PaymentOrder genOrder(
            WorkerJpaManager ebean,
            PaymentProvider provider,
            Account account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            Transaction tx) throws Throwable {
        return genOrder(ebean, provider, account, offerTemplate, platformSpec, code, transactionId, isSuccess, new Date(), true, "USD", 0L, 0L, tx);
    }

    public static PaymentOrder genOrder(
            WorkerJpaManager ebean,
            PaymentProvider provider,
            Account account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            Date date,
            boolean refunded,
            String currency,
            double gcAmount,
            double scAmount,
            Transaction tx) throws Throwable {
        PaymentOrder paymentOrder = new PaymentOrder();
        Provider paymentProvider = WorkerPaymentProviderDataGenerator.getPaymentProvider(ebean, provider, tx);
        paymentOrder.setId((long) ebean.idGenerator(PaymentOrderSeq.class).nextId(tx));
        paymentOrder.setProvider(paymentProvider);
        paymentOrder.setCode(code);
        paymentOrder.setOrderSn(PlatformUtil.randomUUID().toString());
        paymentOrder.setTransactionId(transactionId);
        paymentOrder.setAccount(ebean.paymentAccountRepo().requiredAccount(account.getId(), tx));
        paymentOrder.setAmount(offerTemplate.getPrice());
        paymentOrder.setBaseAmount(offerTemplate.getPrice());
        paymentOrder.setCurrency(currency);
        paymentOrder.setUserAgent("User Agent");
        paymentOrder.setPlatform(platformSpec);
        paymentOrder.setDescription(offerTemplate.getTitle());
        paymentOrder.setOffer(offerTemplate);
        paymentOrder.setSuccess(isSuccess);
        paymentOrder.setRemoteIp("127.0.0.1");
        paymentOrder.setAt(PlatformUtil.toLocalUTCDate());
        paymentOrder.setRefunded(refunded);
        paymentOrder.setFraudScore(10);
        paymentOrder.setGcAmount(BigDecimal.valueOf(gcAmount));
        paymentOrder.setScAmount(BigDecimal.valueOf(scAmount));
        ebean.save(paymentOrder);

        paymentOrder.setCreatedAt(date);
        ebean.save(paymentOrder, tx);

        return paymentOrder;
    }

    public static PaymentOrder genSuccessfulOrder(WorkerJpaManager ebean, Account account, Transaction tx) throws Throwable {
        OfferTemplate offerTemplate = WorkerOfferDataGenerator.getOffer(ebean, tx);
        return genSuccessfulOrder(ebean, account, offerTemplate, PaymentProvider.SKRILL, tx);
    }

    public static WithdrawMoneyRequest genWithdraw(WorkerJpaManager ebean, Account account,
                                                   PaymentProvider provider, RedeemStatusSpec status, BigDecimal amount, Transaction tx) throws Throwable {
        WithdrawMoneyRequest wmr = new WithdrawMoneyRequest();
        ImmutableAccount paymentAccount = ebean.paymentAccountRepo().requiredAccount(account.getId(), tx);
        Provider paymentProvider = WorkerWithdrawProviderDataGenerator.getWithdrawProvider(ebean, provider, tx);
        AccountWithdrawMethod withdrawMethod = genWithdrawMethod(ebean, paymentAccount, tx);

        wmr.setProvider(paymentProvider);
        wmr.setMethod(withdrawMethod);
        wmr.setCode(PlatformUtil.randomUUID().toString());
        wmr.setTransactionId(PlatformUtil.randomUUID());
        wmr.setAccount(paymentAccount);
        wmr.setCurrency(account.getBrand().getSweepstakeCurrency());
        wmr.setStatus(status);
        wmr.setUamStatus(status);
        wmr.setAmount(amount);
        wmr.setBaseAmount(amount);
        wmr.setEmail(account.getEmail());
        wmr.setAt(PlatformUtil.toLocalUTCDate());
        ebean.save(wmr, tx);

        return wmr;
    }

    private static AccountWithdrawMethod genWithdrawMethod(WorkerJpaManager ebean, ImmutableAccount account, Transaction tx) {

        AccountWithdrawMethod method = new AccountWithdrawMethod(account, false);
        method.setType(WithdrawMethodSpec.CRYPTO);
        method.setCode("code");
        ebean.save(method, tx);

        return method;
    }

    public static WithdrawMoneyRequest genSuccessfulWithdraw(WorkerJpaManager ebean, Account account, Transaction tx) throws Throwable {
        return genWithdraw(ebean, account, PaymentProvider.SKRILL, RedeemStatusSpec.CONFIRMED, BigDecimal.TEN, tx);
    }
}
