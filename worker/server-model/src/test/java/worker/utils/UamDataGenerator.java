package worker.utils;

import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URI;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Currency;
import java.util.Date;
import java.util.Locale;
import java.util.Map;

import com.google.common.hash.Hashing;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.EbeanJpaManager;
import com.turbospaces.ebean.JpaManager;

import api.v1.AccountStatusSpec;
import api.v1.ForcementModeSpec;
import api.v1.KYCStatusSpec;
import api.v1.PlatformSpec;
import api.v1.ProviderSpec;
import api.v1.SignMethodSpec;
import io.ebean.Transaction;
import uam.model.Account;
import uam.model.AccountAuthInfo;
import uam.model.AccountGameplayInfo;
import uam.model.AccountLegalRule;
import uam.model.AccountMetaInfo;
import uam.model.AccountPersonalInfo;
import uam.model.AccountPreferences;
import uam.model.BonusReward;
import uam.model.Brand;
import uam.model.ExperienceLevel;
import uam.model.ExperienceLevelTypeSpec;
import uam.model.GoldMoneyPolicy;
import uam.model.ImmutableProduct;
import uam.model.InternalProductSpec;
import uam.model.LegalRule;
import uam.model.LegalRuleSettingSpec;
import uam.model.PasswordPolicy;
import uam.model.Person;
import uam.model.RuleTypeSpec;
import uam.model.SweepstakeMoneyPolicy;
import uam.model.VipLevel;
import worker.WorkerEbeanJpaManager;

public class UamDataGenerator {

    private static final String TEST_BRAND = "bluedream";
    private static final String SC_CURRENCY = "SC";
    private static final String FIAT_CURRENCY = "GBP";

    public static void cleanupEntitiesTables(JpaManager jpaManager, Class<?>... entities) {
        try {
            jpaManager.execute(() -> {
                for (Class<?> clazz : entities) {
                    jpaManager.truncate(clazz);
                }
            });
        } finally {
            jpaManager.cacheManager().clearAll();
        }
    }

    public static Brand createBrand(String brandName, ForcementModeSpec forcementModeSpec,
                                    GoldMoneyPolicy goldMoneyPolicy, SweepstakeMoneyPolicy realMoneyPolicy) throws MalformedURLException {
        Brand brand = new Brand();
        brand.setMode(forcementModeSpec);
        brand.setName(brandName);
        brand.setHomePage(URI.create("http://localhost").toURL());
        brand.setGatewayUrl(URI.create("http://localhost").toURL());
        brand.setWebhookUrl(URI.create("http://localhost").toURL());
        brand.setTitle(brand.getName());
        brand.setGoldCurrency("GC");
        brand.setSweepstakeCurrency(SC_CURRENCY);
        brand.setFiatCurrency(FIAT_CURRENCY);
        brand.setGoldMoneyPolicy(goldMoneyPolicy);
        brand.setSweepstakeMoneyPolicy(realMoneyPolicy);
        PasswordPolicy policy = new PasswordPolicy();
        brand.setPasswordPolicy(policy);
        brand.setActive(true);
        brand.setDomain(String.join(".", brandName, "com"));

        ExperienceLevel experienceLevel = createXpLevel(brand);
        brand.addXpLevel(experienceLevel);
        VipLevel vipLevel = createVipLevel(brand);
        brand.addVipLevel(vipLevel);

        return brand;
    }

    private static ExperienceLevel createXpLevel(Brand brand) {
        ExperienceLevel experienceLevel = new ExperienceLevel(brand, 0);
        experienceLevel.setType(ExperienceLevelTypeSpec.PERMANENT);
        experienceLevel.setPointsToNextLevel(BigDecimal.TEN);
        experienceLevel.setPoints(BigDecimal.TEN);
        experienceLevel.setBonus(BigDecimal.TEN);
        experienceLevel.setVipPoints(BigDecimal.TEN);

        return experienceLevel;
    }

    private static VipLevel createVipLevel(Brand brand) {
        VipLevel vipLevel = new VipLevel(brand, 0);
        vipLevel.setName("VIP");
        vipLevel.setPointsToNextLevel(BigDecimal.TEN);
        vipLevel.setPoints(BigDecimal.TEN);

        return vipLevel;
    }

    public static void genVipLevels(Brand brand, EbeanJpaManager ebean, Transaction tx) {
        BigDecimal vipPoints = BigDecimal.ZERO;
        for (int i = 0; i < 4; i++) {
            VipLevel level = genVipLevel(ebean, brand, i, vipPoints, tx);
            vipPoints = vipPoints.add(level.getPointsToNextLevel());
        }
    }

    public static void genXpLevels(Brand brand, EbeanJpaManager ebean, Transaction tx) {
        ExperienceLevel inheritFrom = null;
        BigDecimal freePoints = BigDecimal.ZERO;
        for (int i = 0; i < 8; i++) {
            ExperienceLevel level = genGoldMoneyLevel(ebean, brand, i, freePoints, inheritFrom, tx);
            freePoints = freePoints.add(level.getPointsToNextLevel());
            inheritFrom = level;
        }
    }

    public static VipLevel genVipLevel(EbeanJpaManager ebean, Brand brand, Integer level, BigDecimal sum,
                                       Transaction tx) {
        VipLevel policy = new VipLevel(brand, level);
        policy.setName(level.toString());
        policy.setPointsToNextLevel(BigDecimal.valueOf(level).multiply(BigDecimal.valueOf(10)));
        policy.setPoints(sum.add(policy.getPointsToNextLevel()));
        ebean.save(policy, tx);
        brand.addVipLevel(policy);
        return policy;
    }

    public static ExperienceLevel genGoldMoneyLevel(
            EbeanJpaManager ebean,
            Brand brand,
            Integer level,
            BigDecimal sum,
            ExperienceLevel inheritFrom,
            Transaction tx) {
        ExperienceLevel policy = new ExperienceLevel(brand, level);
        policy.setPointsToNextLevel(BigDecimal.valueOf(level).multiply(BigDecimal.valueOf(50)));
        policy.setPoints(sum.add(policy.getPointsToNextLevel()));
        policy.setBonus(BigDecimal.ZERO);
        policy.setVipPoints(BigDecimal.ONE);
        policy.setInheritOffersFrom(inheritFrom);
        policy.setLevels(Map.of(ProviderSpec.NETENT.code(), level));
        policy.setType(ExperienceLevelTypeSpec.PERMANENT);
        ebean.save(policy, tx);
        brand.addXpLevel(policy);
        return policy;
    }

    public static Account genAccount(JpaManager ebean, Brand brand, Transaction tx) throws Exception {
        Account account = new Account();
        Person person = new Person();
        person.setFirstName("First");
        person.setLastName("Last");
        ebean.save(person, tx);

        var hash = Hashing.murmur3_32_fixed().hashBytes(PlatformUtil.randomUUID().toString().getBytes()).toString();
        account.setBrand(brand);
        account.setUsername("userName" + hash);
        account.email("test" + hash + "@gmail.com");
        account.setCountry(Locale.US.getCountry());
        account.realEmail(account.getEmail());
        account.authEmail(account.getEmail());

        AccountPersonalInfo personalInfo = new AccountPersonalInfo();
        personalInfo.setEmail(account.getEmail());
        personalInfo.setCountry(account.getCountry());
        personalInfo.setFirstName(person.getFirstName());
        personalInfo.setLastName(person.getLastName());
        personalInfo.setCurrency(Currency.getInstance(Locale.US).getCurrencyCode());
        account.setPersonalInfo(personalInfo);

        account.setKyc(KYCStatusSpec.CONFIRMED);
        account.setHash(hash);
        account.setMode(ForcementModeSpec.SWEEPSTAKE);
        account.setPerson(person);
        account.setAt(LocalDate.now(ZoneOffset.UTC));
        account.setStatus(AccountStatusSpec.DEFAULT);
        account.setEmailVerified(true);
        ebean.save(account, tx);

        var metaInfo = new AccountMetaInfo();
        metaInfo.setId(account.getId());
        ebean.save(metaInfo, tx);
        account.setMetaInfo(metaInfo);

        var authInfo = new AccountAuthInfo();
        authInfo.setId(account.getId());
        authInfo.setSignUpMethod(SignMethodSpec.MANUAL.code());
        authInfo.setSignUpIp("127.0.0.1");
        authInfo.setSignUp(new Date());
        authInfo.setSignUpLang(Locale.US.getLanguage());
        authInfo.setSignUpPlatform(PlatformSpec.WEB);
        ebean.save(authInfo, tx);
        account.setAuthInfo(authInfo);

        AccountPreferences preferences = new AccountPreferences();
        preferences.setId(account.getId());
        preferences.setLang(Locale.US.getLanguage());
        preferences.setTimezone("UTC");
        preferences.setLocale("en-US");
        account.setPreferences(preferences);
        ebean.save(preferences, tx);

        AccountGameplayInfo gameplayInfo = new AccountGameplayInfo(account);
        ebean.save(gameplayInfo, tx);

        LegalRule rule = new LegalRule();
        rule.setBrand(brand);
        rule.setType(RuleTypeSpec.PP);
        rule.setRuleVersion(1);
        rule.setSetting(LegalRuleSettingSpec.POPUP);
        rule.setVersionDate(LocalDate.now());
        ebean.save(rule, tx);

        AccountLegalRule accountLegalRule = new AccountLegalRule(account, rule);
        ebean.save(accountLegalRule, tx);

        return account;
    }

    public static BonusReward genBonusReward(WorkerEbeanJpaManager ebean, Account account, Transaction tx) throws Exception {
        BonusReward reward = new BonusReward();
        reward.setAccount(account);
        reward.setCode("code");
        reward.setProduct(getProduct(ebean, tx));
        reward.setAt(LocalDate.now());
        ebean.save(reward, tx);

        return reward;
    }

    private static ImmutableProduct getProduct(WorkerEbeanJpaManager ebean, Transaction tx) throws Exception {
        Brand brand = ebean.uamBrandRepo().requiredBrandByName(TEST_BRAND, tx);
        return ebean.uamBrandRepo().requiredProductByCode(brand, InternalProductSpec.CASHBACK.code(), tx);
    }
}
