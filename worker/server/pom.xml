<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>worker-parent</artifactId>
        <groupId>com.turbospaces.uam</groupId>
        <version>25.07.2-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>worker-server</artifactId>
    <name>worker ::: ${project.artifactId}</name>
    <dependencies>
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-temporal-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-ratelimiter</artifactId>
        </dependency>
             <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-healthchecks</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>io.opentracing</groupId>
            <artifactId>opentracing-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.inject</groupId>
            <artifactId>jakarta.inject-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>client-v2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.reactivestreams</groupId>
            <artifactId>reactive-streams</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-database-postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-netty-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-clickhouse-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-ebean-migration</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-jdbc-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-quartz-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-kafka-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius2-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius2-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>io.ebean</groupId>
            <artifactId>jakarta-persistence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.ebean</groupId>
            <artifactId>ebean-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>io.ebean</groupId>
            <artifactId>ebean-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.ebean</groupId>
            <artifactId>ebean-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.antlr</groupId>
                    <artifactId>antlr4-runtime</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendgrid</groupId>
            <artifactId>sendgrid-java</artifactId>
        </dependency>
        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-api-core</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>worker-server-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>payment-server-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>fraud-server-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-server-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-server-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>core-server-model</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>worker-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>worker-server-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>fraud-server-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-facade-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-identity-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-types-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-spec-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-api-flatten-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>gateway-server-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>fraud-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>payment-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-server-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-server-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>payment-server-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>server-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.uam</groupId>
            <artifactId>bi-box</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-prodege</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-bloomreach</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-graphyte</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-bankrolls</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-flows</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.uam</groupId>
            <artifactId>bi-xe-rate</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-protobuf-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-httpclient-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-redis-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.ebean</groupId>
            <artifactId>ebean-platform-postgres</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-server-api-common</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-sendgrid</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-rapid-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-sidelines</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-intelitics</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-voluum</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-converttologic</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-moonshoot</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-snapchat</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-tiktok</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-clickhouse</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-bi-sendgrid-client</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>worker-bi-appsflyer</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-facebook</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-pinterest</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-gtm</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-reddit</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-twitter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>bi-make</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facebook.business.sdk</groupId>
            <artifactId>facebook-java-business-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-product-provider-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-social-google</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-gaming-markortech</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jexl3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-gaming-netent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-gaming-patrianna</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-gaming-pragmatic</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>game-hub-gaming-relax</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api-ads</groupId>
            <artifactId>google-ads-stubs-v18</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.http-client</groupId>
            <artifactId>google-http-client</artifactId>
        </dependency>
        <!-- -->
        <!-- provided -->
        <!-- -->
        <dependency>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
            <scope>provided</scope>
        </dependency>
        <!-- -->
        <!-- runtime -->
        <!-- -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-database-clickhouse</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!-- -->
        <!-- test -->
        <!-- -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-client-java</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-commons</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-core</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.ebean</groupId>
            <artifactId>ebean-platform-h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-netty</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.github.hakky54</groupId>
            <artifactId>logcaptor</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-core</artifactId>
            <classifier>tests</classifier>
            <version>${boot.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-ebean-migration</artifactId>
            <classifier>tests</classifier>
            <version>${boot.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-server</artifactId>
            <classifier>tests</classifier>
            <type>test-jar</type>
            <scope>test</scope>
            <version>${crm.version}</version>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>jdbc</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.ws.rs</groupId>
            <artifactId>jakarta.ws.rs-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>clickhouse</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>io.ebean</groupId>
                <artifactId>ebean-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
