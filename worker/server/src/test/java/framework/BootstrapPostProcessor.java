package framework;

import java.util.Properties;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;

import com.turbospaces.cfg.ApplicationConfig;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class BootstrapPostProcessor implements BeanDefinitionRegistryPostProcessor {
    private final ApplicationConfig cfg;
    private final Properties properties;

    @Override
    public void postProcessBeanDefinitionRegistry(@NonNull BeanDefinitionRegistry registry) throws BeansException {
        cfg.setDefaultProperties(properties);
    }
}
