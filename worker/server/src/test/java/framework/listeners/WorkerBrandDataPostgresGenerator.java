package framework.listeners;

import java.util.List;

import org.springframework.test.context.TestContext;

import com.turbospaces.ebean.JpaManager;

import gamehub.model.Product;
import gamehub.model.ProductSupplier;
import uam.model.Account;
import uam.model.BonusReward;
import uam.model.Brand;
import uam.model.ExperienceLevel;
import uam.model.LoyaltyLevelBonus;
import uam.model.Provider;
import uam.model.VipLevel;
import uam.model.WalletSession;
import worker.WorkerEbeanJpaManager;
import worker.WorkerMasterEbeanJpaManager;

public class WorkerBrandDataPostgresGenerator extends WorkerBrandDataGenerator {

    @Override
    public void afterTestMethod(TestContext testContext) {
        WorkerEbeanJpaManager ebean = testContext.getApplicationContext().getBean(WorkerMasterEbeanJpaManager.class);
        cleanupAllEntitiesTables(ebean, List.of(WalletSession.class,
                BonusReward.class,
                Account.class,
                VipLevel.class,
                LoyaltyLevelBonus.class,
                ExperienceLevel.class,
                Product.class,
                ProductSupplier.class,
                Provider.class,
                Brand.class)
        );
    }

    private static void cleanupAllEntitiesTables(JpaManager jpaManager, List<Class<?>> entities) {
        try {
            jpaManager.execute(() -> entities.forEach(entity -> {
                List<?> l = jpaManager.find(entity).orderById(true).findList().reversed();
                jpaManager.deleteAll(l);
            }));
        } finally {
            jpaManager.cacheManager().clearAll();
        }
    }
}
