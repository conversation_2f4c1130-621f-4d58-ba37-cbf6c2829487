package framework.listeners;

import java.util.Optional;

import org.springframework.test.context.TestContext;
import org.springframework.test.context.TestExecutionListener;

import com.turbospaces.ebean.DataGeneratorUtil;

import io.ebean.Transaction;
import lombok.SneakyThrows;
import model.Schemas;
import payment.model.Provider;
import payment.model.ProviderIntegrationTypeSpec;
import payment.model.ProviderTypeSpec;
import payment.model.immutable.ImmutableBrand;
import payment.type.RedeemProviderSpec;
import uam.api.v1.PaymentProvider;
import worker.WorkerEbeanJpaManager;
import worker.WorkerJpaManager;
import worker.WorkerMasterEbeanJpaManager;

public class WorkerWithdrawProviderDataGenerator implements TestExecutionListener {

    public static Provider getWithdrawProvider(WorkerJpaManager ebean, PaymentProvider paymentProvider, Transaction tx) {
        Provider provider = ebean.find(Provider.class)
                .usingTransaction(tx)
                .where()
                .eq("code", paymentProvider.name().toLowerCase())
                .and()
                .eq("type", ProviderTypeSpec.WITHDRAW.code())
                .findOne();

        return Optional
            .ofNullable(provider)
            .orElseThrow(() -> new IllegalStateException("Can't find withdraw Provider with code [" + paymentProvider.name().toLowerCase() + "]"));
    }

    public static void genWithdrawProvider(WorkerJpaManager ebean, RedeemProviderSpec wdProvider, ProviderIntegrationTypeSpec integrationType, Transaction tx) {
        ImmutableBrand brand = getBrand(ebean, tx);
        Provider provider = Provider.builder()
                .code(wdProvider.name().toLowerCase())
                .brand(brand)
                .type(ProviderTypeSpec.WITHDRAW)
                .integrationType(integrationType)
                .inactive(false)
                .paymentMode(wdProvider.getPaymentMode())
                .description("some description")
                .displayName(wdProvider.name())
                .billingDescriptor("Bluedream LTD")
                .build();
        ebean.save(provider, tx);
    }

    @Override
    @SneakyThrows
    public void beforeTestMethod(TestContext testContext) {
        WorkerEbeanJpaManager ebean = testContext.getApplicationContext().getBean(WorkerMasterEbeanJpaManager.class);
        try (Transaction tx = ebean.newTransaction()) {
            genWithdrawProvider(ebean, RedeemProviderSpec.SKRILL, ProviderIntegrationTypeSpec.SKRILL, tx);
            tx.commit();
        }
    }

    @Override
    public void afterTestMethod(TestContext testContext) {
        WorkerEbeanJpaManager ebean = testContext.getApplicationContext().getBean(WorkerMasterEbeanJpaManager.class);
        DataGeneratorUtil.clearH2Db(ebean, new String[] { Schemas.PAYMENT, Schemas.UAM, Schemas.CORE });
    }

    @SneakyThrows
    private static ImmutableBrand getBrand(WorkerJpaManager ebean, Transaction tx) {
        return ebean.paymentBrandRepo().requiredBrandByName(WorkerBrandDataGenerator.BRAND, tx);
    }
}
