package framework.util;

import static uam.api.v1.PaymentProvider.SPREEDLY_FISERV;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

import com.turbospaces.clickhouse.ClickhouseClient;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.json.CommonObjectMapper;

import api.v1.ForcementModeSpec;
import api.v1.PlatformSpec;
import framework.listeners.WorkerBrandDataGenerator;
import framework.listeners.WorkerPaymentProviderDataGenerator;
import framework.listeners.WorkerWithdrawProviderDataGenerator;
import io.ebean.Transaction;
import payment.PaymentOrderSeq;
import payment.model.AccountMetaInfo;
import payment.model.AccountWithdrawMethod;
import payment.model.OfferTemplate;
import payment.model.PaymentOrder;
import payment.model.Provider;
import payment.model.ProviderIntegrationTypeSpec;
import payment.model.ProviderTypeSpec;
import payment.model.RedeemStatusSpec;
import payment.model.WithdrawMethodSpec;
import payment.model.WithdrawMoneyRequest;
import payment.model.immutable.ImmutableAccount;
import payment.model.immutable.ImmutableBrand;
import payment.type.PurchaseProviderSpec;
import uam.UamDataGenerator;
import uam.api.v1.PaymentProvider;
import uam.model.Account;
import uam.model.AccountEngagementInfo;
import uam.model.AccountGameplayInfo;
import uam.model.BonusReward;
import uam.model.Brand;
import uam.model.ExperienceLevel;
import uam.model.ExperienceLevelTypeSpec;
import uam.model.ImmutableProduct;
import uam.model.LoyaltyLevelBonus;
import uam.model.VipLevel;
import uam.model.WalletSession;
import uam.model.WalletSessionPK;
import uam.model.WalletSessionTypeSpec;
import worker.WorkerJpaManager;
import worker.clickhouse.WalletSessionReplica;

public class DataGenerationService {

    public static Account genAccount(WorkerJpaManager ebean, Transaction tx) throws Throwable {
        Brand brand = ebean.uamBrandRepo().requiredBrandByName(WorkerBrandDataGenerator.BRAND, tx);
        return UamDataGenerator.genAccount(ebean, brand, tx);
    }

    public static Account genAccount(WorkerJpaManager ebean, Brand brand, Transaction tx) throws Throwable {
        return UamDataGenerator.genAccount(ebean, brand, tx);
    }

    public static void genAccountMetaInfo(
            WorkerJpaManager ebean,
            ImmutableAccount account,
            PaymentOrder successOrder,
            PaymentOrder failedOrder,
            Transaction tx) {
        AccountMetaInfo metaInfo = new AccountMetaInfo();
        metaInfo.setAccount(account);
        metaInfo.setLastSuccessfulPaymentOrder(successOrder);
        metaInfo.setLastFailedOnProviderPaymentOrder(failedOrder);
        ebean.save(metaInfo, tx);
    }

    public static void genBonusReward(WorkerJpaManager ebean, Account account, String code, LocalDate now, Transaction tx) throws Throwable {
        ImmutableProduct product = ebean.uamBrandRepo().requiredProductByCode(account.getBrand(), code, tx);

        BonusReward bonusReward = new BonusReward(account, PlatformUtil.randomUUID().toString(), product, now);
        bonusReward.setGoldAmount(BigDecimal.TEN);
        bonusReward.setSweepstakeAmount(BigDecimal.ONE);
        bonusReward.setAccepted(false);

        ebean.save(bonusReward, tx);
    }

    public static Account genAccountWithVipLevel(WorkerJpaManager ebean, ForcementModeSpec forcementModeSpec, Date now, Transaction tx) throws Throwable {
        var brand = ebean.uamBrandRepo().requiredBrandByName(WorkerBrandDataGenerator.BRAND, tx);
        var account = UamDataGenerator.genAccount(ebean, brand, tx);
        account.setMode(forcementModeSpec);
        ebean.save(account, tx);

        AccountGameplayInfo accountGameplayInfo = account.getGameplayInfo();
        accountGameplayInfo.setLastGameplay(now);
        accountGameplayInfo.setLastGcGameplay(now);
        accountGameplayInfo.setLastScGameplay(now);
        ebean.save(accountGameplayInfo, tx);

        AccountEngagementInfo engagementInfo = account.getEngagementInfo();

        VipLevel vipLevel = new VipLevel(brand, account.getId().intValue() + 10);
        vipLevel.setPointsToNextLevel(BigDecimal.valueOf(10L));
        vipLevel.setPoints(BigDecimal.valueOf(10L));
        vipLevel.setName("vip");
        ebean.save(vipLevel, tx);

        engagementInfo.setVipLevel(vipLevel);
        engagementInfo.setMonthlyGoldPoints(BigDecimal.ZERO);
        engagementInfo.setAnnualGoldPoints(BigDecimal.ZERO);
        engagementInfo.setPointsUpdatedAt(PlatformUtil.toLocalUTCDate());
        account.setEngagementInfo(engagementInfo);

        ExperienceLevel level = new ExperienceLevel();
        level.setBrand(brand);
        level.setLevel(account.getId().intValue() + 10);
        level.setName("bronze");
        level.setBonus(BigDecimal.valueOf(10L));
        level.setPointsToNextLevel(BigDecimal.valueOf(1000L));
        level.setPoints(BigDecimal.valueOf(10L));
        level.setVipPoints(BigDecimal.valueOf(10L));
        level.setType(ExperienceLevelTypeSpec.MONTH);
        level.setCashbackRate(BigDecimal.valueOf(0.02));
        account.getEngagementInfo().setXpLevel(level);

        ebean.save(level, tx);

        ebean.save(account.getEngagementInfo(), tx);

        LoyaltyLevelBonus loyaltyLevelBonusTen = new LoyaltyLevelBonus();
        loyaltyLevelBonusTen.setXpLevel(level);
        loyaltyLevelBonusTen.setBonusGc(BigDecimal.valueOf(1000L));
        loyaltyLevelBonusTen.setBonusSc(BigDecimal.valueOf(10L));
        loyaltyLevelBonusTen.setNetGamingRevenue(BigDecimal.TEN);
        ebean.save(loyaltyLevelBonusTen, tx);

        LoyaltyLevelBonus loyaltyLevelBonusHundred = new LoyaltyLevelBonus();
        loyaltyLevelBonusHundred.setXpLevel(level);
        loyaltyLevelBonusHundred.setBonusGc(BigDecimal.valueOf(1000L));
        loyaltyLevelBonusHundred.setBonusSc(BigDecimal.valueOf(100L));
        loyaltyLevelBonusHundred.setNetGamingRevenue(BigDecimal.valueOf(100L));
        ebean.save(loyaltyLevelBonusHundred, tx);

        LoyaltyLevelBonus loyaltyLevelBonusThousand = new LoyaltyLevelBonus();
        loyaltyLevelBonusThousand.setXpLevel(level);
        loyaltyLevelBonusThousand.setBonusGc(BigDecimal.valueOf(10000L));
        loyaltyLevelBonusThousand.setBonusSc(BigDecimal.valueOf(1000L));
        loyaltyLevelBonusThousand.setNetGamingRevenue(BigDecimal.valueOf(1000L));
        ebean.save(loyaltyLevelBonusThousand, tx);

        LoyaltyLevelBonus loyaltyLevelBonusTenThousand = new LoyaltyLevelBonus();
        loyaltyLevelBonusTenThousand.setXpLevel(level);
        loyaltyLevelBonusTenThousand.setBonusGc(BigDecimal.valueOf(100000L));
        loyaltyLevelBonusTenThousand.setBonusSc(BigDecimal.valueOf(10000L));
        loyaltyLevelBonusTenThousand.setNetGamingRevenue(BigDecimal.valueOf(10000L));
        ebean.save(loyaltyLevelBonusTenThousand, tx);

        return account;
    }

    public static void genWalletSessions(
            CommonObjectMapper mapper,
            WorkerJpaManager ebean,
            ClickhouseClient clickhouseClient,
            Account account,
            String currency,
            Date now,
            Transaction tx) throws Throwable {
        LocalDate created = now.toInstant().atOffset(ZoneOffset.UTC).toLocalDate();

        List<WalletSession> ws = LongStream.range(0, 10).mapToObj(_ -> createWalletSession(account, currency, created)).toList();
        ebean.insertAll(ws, tx);
        tx.flush();

        List<WalletSessionReplica> wsReplica = ws.stream().map(it -> new WalletSessionReplica(it, mapper)).collect(Collectors.toList());
        clickhouseClient.insert(wsReplica);

        //
        // ~ duplicates for testing optimise table
        //
        wsReplica.forEach(w -> w.setVersion(w.getVersion() + 1));
        clickhouseClient.insert(wsReplica);
    }

    private static WalletSession createWalletSession(Account account, String currency, LocalDate created) {
        WalletSession ws = new WalletSession();
        ws.setPk(new WalletSessionPK(Long.valueOf(PlatformUtil.randomNumeric(10)), created));
        ws.setType(WalletSessionTypeSpec.GAME);
        ws.setAccount(account);
        ws.setSessionId(PlatformUtil.randomUUID().toString());
        ws.setExternalSessionId(ws.getSessionId());
        ws.setSource("internal");
        ws.setCurrency(currency);
        ws.setCreatedAt(Date.from(created.atStartOfDay(ZoneOffset.UTC).toInstant()));
        ws.setProfit(BigDecimal.valueOf(150));
        ws.setLoss(BigDecimal.valueOf(50));
        return ws;
    }

    public static PaymentOrder genSuccessfulOrder(
            WorkerJpaManager ebean,
            Account account,
            OfferTemplate offerTemplate,
            PaymentProvider provider,
            Transaction tx) throws Throwable {
        return genOrder(
                ebean,
                provider,
                account,
                offerTemplate,
                PlatformSpec.WEB,
                PlatformUtil.randomUUID().toString(),
                PlatformUtil.randomUUID(),
                true,
                tx);
    }

    public static PaymentOrder genOrder(
            WorkerJpaManager ebean,
            PaymentProvider provider,
            Account account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            Transaction tx) throws Throwable {
        return genOrder(ebean, provider, account, offerTemplate, platformSpec, code, transactionId, isSuccess, new Date(), true, "USD", 0L, 0L, tx, PlatformUtil.toLocalUTCDate());
    }

    public static PaymentOrder genOrder(
            WorkerJpaManager ebean,
            PaymentProvider provider,
            Account account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            Transaction tx,
            LocalDate at
            ) throws Throwable {
        return genOrder(ebean, provider, account, offerTemplate, platformSpec, code, transactionId, isSuccess, new Date(), true, "USD", 0L, 0L, tx, at);
    }

    public static PaymentOrder genOrder(
            WorkerJpaManager ebean,
            PaymentProvider provider,
            Account account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            String currency,
            Transaction tx) throws Throwable {
        return genOrder(ebean, provider, account, offerTemplate, platformSpec, code, transactionId, isSuccess, new Date(), true, currency, 0L, 0L, tx, PlatformUtil.toLocalUTCDate());
    }

    public static PaymentOrder genOrder(
            WorkerJpaManager ebean,
            PaymentProvider provider,
            Account account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            double gcAmount,
            double scAmount,
            Transaction tx) throws Throwable {
        return genOrder(ebean, provider, account, offerTemplate, platformSpec, code, transactionId, isSuccess, new Date(), true, "USD", gcAmount, scAmount, tx, PlatformUtil.toLocalUTCDate());
    }

    public static void genProvider(WorkerJpaManager ebean, Transaction tx) {
        var provider = new Provider();
        provider.setCode(SPREEDLY_FISERV.toString().toLowerCase());
        provider.setIntegrationType(ProviderIntegrationTypeSpec.SPREEDLY_FISERV);
        provider.setType(ProviderTypeSpec.PURCHASE);
        provider.setPaymentMode(PurchaseProviderSpec.SPREEDLY_FISERV.getPaymentMode());
        provider.setBrand(ebean.find(ImmutableBrand.class).where().eq("name", WorkerBrandDataGenerator.BRAND).findOne());
        provider.setDescription("mid");
        ebean.save(provider, tx);
    }

    public static PaymentOrder genOrder(
            WorkerJpaManager ebean,
            PaymentProvider provider,
            Account account,
            OfferTemplate offerTemplate,
            PlatformSpec platformSpec,
            String code,
            UUID transactionId,
            boolean isSuccess,
            Date date,
            boolean refunded,
            String currency,
            double gcAmount,
            double scAmount,
            Transaction tx,
            LocalDate at) throws Throwable {
        PaymentOrder paymentOrder = new PaymentOrder();
        Provider paymentProvider = WorkerPaymentProviderDataGenerator.getPaymentProvider(ebean, provider, tx);
        paymentOrder.setId((long) ebean.idGenerator(PaymentOrderSeq.class).nextId(tx));
        paymentOrder.setProvider(paymentProvider);
        paymentOrder.setCode(code);
        paymentOrder.setOrderSn(PlatformUtil.randomUUID().toString());
        paymentOrder.setTransactionId(transactionId);
        paymentOrder.setAccount(ebean.paymentAccountRepo().requiredAccount(account.getId(), tx));
        paymentOrder.setAmount(offerTemplate.getPrice());
        paymentOrder.setBaseAmount(offerTemplate.getPrice());
        paymentOrder.setCurrency(currency);
        paymentOrder.setUserAgent("User Agent");
        paymentOrder.setPlatform(platformSpec);
        paymentOrder.setDescription(offerTemplate.getTitle());
        paymentOrder.setOffer(offerTemplate);
        paymentOrder.setSuccess(isSuccess);
        paymentOrder.setRemoteIp("127.0.0.1");
        paymentOrder.setAt(PlatformUtil.toLocalUTCDate());
        paymentOrder.setRefunded(refunded);
        paymentOrder.setFraudScore(10);
        paymentOrder.setGcAmount(BigDecimal.valueOf(gcAmount));
        paymentOrder.setScAmount(BigDecimal.valueOf(scAmount));
        paymentOrder.setAt(at);
        ebean.save(paymentOrder);

        paymentOrder.setCreatedAt(date);
        ebean.save(paymentOrder, tx);

        return paymentOrder;
    }

    public static PaymentOrder genSuccessfulOrder(
            WorkerJpaManager ebean,
            ImmutableAccount account,
            OfferTemplate offerTemplate,
            PaymentProvider provider,
            Transaction tx) throws Throwable {
        Account uamAccount = ebean.uamAccountRepo().requiredAccount(account.getId(), tx);
        return genSuccessfulOrder(ebean, uamAccount, offerTemplate, provider, tx);
    }

    public static PaymentOrder genSuccessfulOrder(
            WorkerJpaManager ebean,
            ImmutableAccount account,
            OfferTemplate offerTemplate,
            PaymentProvider provider,
            LocalDate at,
            Transaction tx) throws Throwable {
        Account uamAccount = ebean.uamAccountRepo().requiredAccount(account.getId(), tx);
       return genOrder(ebean, provider, uamAccount, offerTemplate, PlatformSpec.WEB,  PlatformUtil.randomUUID().toString(), PlatformUtil.randomUUID(), true, new Date(), true, "USD", 0L, 0L, tx, at);
    }

    public static void genWithdraw(WorkerJpaManager ebean, Account account,
                                                   PaymentProvider provider, RedeemStatusSpec status, BigDecimal amount, Transaction tx) throws Throwable {
        WithdrawMoneyRequest wmr = new WithdrawMoneyRequest();
        ImmutableAccount paymentAccount = ebean.paymentAccountRepo().requiredAccount(account.getId(), tx);
        Provider paymentProvider = WorkerWithdrawProviderDataGenerator.getWithdrawProvider(ebean, provider, tx);
        AccountWithdrawMethod withdrawMethod = genWithdrawMethod(ebean, paymentAccount, tx);

        wmr.setProvider(paymentProvider);
        wmr.setMethod(withdrawMethod);
        wmr.setCode(PlatformUtil.randomUUID().toString());
        wmr.setTransactionId(PlatformUtil.randomUUID());
        wmr.setAccount(paymentAccount);
        wmr.setCurrency(account.getBrand().getSweepstakeCurrency());
        wmr.setStatus(status);
        wmr.setUamStatus(status);
        wmr.setAmount(amount);
        wmr.setBaseAmount(amount);
        wmr.setEmail(account.getEmail());
        wmr.setAt(PlatformUtil.toLocalUTCDate());

        ebean.save(wmr, tx);
    }

    private static AccountWithdrawMethod genWithdrawMethod(WorkerJpaManager ebean, ImmutableAccount account, Transaction tx) {

        AccountWithdrawMethod method = new AccountWithdrawMethod(account, false);
        method.setType(WithdrawMethodSpec.CRYPTO);
        method.setCode("code");
        ebean.save(method, tx);

        return method;
    }

    public static void genSuccessfulWithdraw(WorkerJpaManager ebean, Account account, Transaction tx) throws Throwable {
        genWithdraw(ebean, account, PaymentProvider.SKRILL, RedeemStatusSpec.CONFIRMED, BigDecimal.TEN, tx);
    }
}
