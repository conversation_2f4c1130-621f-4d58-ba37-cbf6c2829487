package framework.util;

import java.math.BigDecimal;

import payment.model.VipLevelPurchaseTemplate;
import payment.model.immutable.ImmutableBrand;
import worker.WorkerJpaManager;

public class VipLevelTemplateDataGenerator {

    public static VipLevelPurchaseTemplate genVipLevelTemplate(ImmutableBrand brand,
            String vipLevel,
            BigDecimal from,
            BigDecimal to,
            WorkerJpaManager ebean) throws Throwable {
        return genVipLevelTemplate(brand, vipLevel, from, to, null, null, null, null, ebean);
    }

    public static VipLevelPurchaseTemplate genVipLevelTemplate(ImmutableBrand brand,
            String vipLevel,
            BigDecimal from,
            BigDecimal to,
            BigDecimal first10,
            BigDecimal firstDay,
            BigDecimal firstWeek,
            BigDecimal first2Weeks,
            WorkerJpaManager ebean) throws Throwable {
        VipLevelPurchaseTemplate vlpt = new VipLevelPurchaseTemplate();
        try (var trx = ebean.newTransaction()) {
            vlpt.setBrand(brand);
            vlpt.setCode(vipLevel);
            vlpt.setFromTotal(from);
            vlpt.setToTotal(to);
            vlpt.setFirst10Total(first10);
            vlpt.setFirstDayTotal(firstDay);
            vlpt.setFirstWeekTotal(firstWeek);
            vlpt.setFirst2WeeksTotal(first2Weeks);
            vlpt.setInactive(true);
            ebean.save(vlpt, trx);
            trx.commit();
        }
        return vlpt;
    }
}
