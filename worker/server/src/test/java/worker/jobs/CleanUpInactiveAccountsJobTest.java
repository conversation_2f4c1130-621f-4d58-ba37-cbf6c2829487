package worker.jobs;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Date;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.EnumSource;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.common.PlatformUtil;

import api.v1.ApiFactory;
import framework.listeners.WorkerBrandDataGenerator;
import framework.listeners.WorkerOfferDataGenerator;
import framework.listeners.WorkerPaymentProviderDataGenerator;
import framework.listeners.WorkerWithdrawProviderDataGenerator;
import framework.util.DataGenerationService;
import io.ebean.Transaction;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.SneakyThrows;
import payment.api.PaymentServiceApi;
import payment.model.OfferTemplate;
import uam.api.UamServiceApi;
import uam.api.v1.CleanUpAccountBalanceResponse;
import uam.api.v1.CleanUpInactiveAccountNotificationResponse;
import uam.api.v1.CleanUpInactiveAccountResponse;
import uam.api.v1.PaymentProvider;
import uam.api.v1.ResetCleanUpStatusResponse;
import uam.model.Account;
import uam.model.AccountBalance;
import uam.model.AccountLegalRule;
import uam.model.AccountMetaInfo;
import uam.model.BonusReward;
import uam.model.Brand;
import uam.model.CleanUpStatusSpec;
import uam.model.ImmutableProduct;
import uam.model.InternalProductSpec;
import uam.model.LegalRule;
import uam.model.LegalRuleSettingSpec;
import uam.model.RuleTypeSpec;
import worker.WorkerMasterEbeanJpaManager;
import worker.WorkerServerProperties;
import worker.WorkerSlaveEbeanJpaManager;
import worker.WorkerSpringBootTestContextBootstrapper;
import worker.di.CommonWorkerDiModule;
import worker.di.MockCommonWorkerDiModule;
import worker.di.MockWorkerDatabaseDiModule;
import worker.di.WorkerQuartzDiModule;
import worker.di.WorkerServiceDiModule;
import worker.model.CleanUpJobSummary;
import worker.services.InactiveAccountsService;

@ExtendWith(SpringExtension.class)
@BootstrapWith(WorkerSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = {
        MockWorkerDatabaseDiModule.class,
        WorkerQuartzDiModule.class,
        CommonWorkerDiModule.class,
        MockCommonWorkerDiModule.class,
        WorkerServiceDiModule.class,
        CleanUpInactiveAccountsJobTest.JobConfiguration.class
})
@TestExecutionListeners(
        mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS,
        listeners = {
                WorkerBrandDataGenerator.class,
                WorkerPaymentProviderDataGenerator.class,
                WorkerOfferDataGenerator.class,
                WorkerWithdrawProviderDataGenerator.class
        }
)
class CleanUpInactiveAccountsJobTest {

    private static final String TEST_BRAND = "bluedream";
    private static final String TEST_BRAND_2 = "testbrand";
    private static final int CLEAN_UP_TC_VERSION = 32;

    @Autowired
    private MockUtil mockUtil;

    @Autowired
    private ApplicationConfig cfg;

    @MockitoBean
    private UamServiceApi uamApi;

    @MockitoBean
    private PaymentServiceApi paymentApi;

    @Autowired
    private WorkerMasterEbeanJpaManager ebean;

    @Autowired
    private WorkerServerProperties props;

    @Autowired
    private CleanUpInactiveAccountsJob job;

    @TestConfiguration
    static class JobConfiguration {

        @Bean
        public CleanUpInactiveAccountsJob cleanUpInactiveAccountsJob(
                WorkerServerProperties props,
                MeterRegistry meterRegistry,
                Scheduler scheduler,
                WorkerSlaveEbeanJpaManager ebean,
                PaymentServiceApi paymentServiceApi,
                UamServiceApi uamServiceApi,
                InactiveAccountsService inactiveAccountsService,
                Clock clock) {

            return new CleanUpInactiveAccountsJob(
                    props,
                    meterRegistry,
                    scheduler,
                    ebean,
                    paymentServiceApi,
                    uamServiceApi,
                    inactiveAccountsService,
                    clock);
        }
    }

    @BeforeEach
    void init() throws Throwable {
        cfg.setLocalProperty(props.CLEANUP_INACTIVE_ACCOUNT_AB_TESTING_PERCENTAGE.getKey(), 100);
        cfg.setLocalProperty("%s.%s".formatted(TEST_BRAND, props.CLEANUP_INACTIVE_ACCOUNT_TC_VERSION.getKey()), CLEAN_UP_TC_VERSION);
        cfg.setLocalProperty(props.CLEANUP_INACTIVE_ACCOUNT_LIMIT.getKey(), 3);
        cfg.clearLocalProperty("cleanup-inactive-accounts.disabled-brands");

        TimeZone.setDefault(TimeZone.getTimeZone("GMT"));

        ebean.truncate(CleanUpJobSummary.class);

        mockServicesApi();
    }

    @Test
    void complexTest() throws Throwable {

        Account account1, account2, account3;

        try (Transaction tx = ebean.newTransaction()) {
            account1 = createAccountWithTcRule(tx);
            updateCreatedAt(account1, props.CLEANUP_INACTIVE_ACCOUNT_FIRST_WARN_DURATION.get(), tx);

            account2 = createAccountWithTcRule(tx);
            updateCreatedAt(account2, props.CLEANUP_INACTIVE_ACCOUNT_FIRST_WARN_DURATION.get(), tx);

            account3 = createAccountForNewBrand(tx);
            updateCreatedAt(account3, props.CLEANUP_INACTIVE_ACCOUNT_FIRST_WARN_DURATION.get(), tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi, never()).resetCleanUpStatus(any(), any());
        verify(uamApi, times(3)).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        var summaries = ebean.find(CleanUpJobSummary.class).findList();
        assertEquals(2, summaries.size());

        CleanUpJobSummary summary1 = summaries.getFirst();
        assertCleanUpJobSummary(summary1, account1.getBrand().getId(), 2);

        CleanUpJobSummary summary2 = summaries.getLast();
        assertCleanUpJobSummary(summary2, account3.getBrand().getId(), 1);
    }

    @Test
    void userNeverPlayedAndWasActiveLongAgoAndHasRecentTc_sendEmailButDoNotCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            var account = createAccountWithTcRule(tx);
            updateCreatedAt(account, props.CLEANUP_INACTIVE_ACCOUNT_FIRST_WARN_DURATION.get(), tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi, never()).resetCleanUpStatus(any(), any());
        verify(uamApi).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertEquals(1, summary.getUsersSentFirstWarning());
            assertZero(summary.getUsersSentSecondWarning());
            assertZero(summary.getUsersReactivatedAfterFirstWarning());
            assertZero(summary.getUsersReactivatedAfterSecondWarning());
            assertZero(summary.getUsersCleanedUp());
            assertZero(summary.getTotalScExpired());
            assertZero(summary.getTotalGcExpired());
        });
    }

    @ParameterizedTest
    @CsvSource({
            "0,10",
            "10,0",
            "10,10"
    })
    void userPlayedLongAgoAndHasRecentTc_sendEmailButDoNotCleanUpBalance(BigDecimal sc, BigDecimal gc) throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(sc, gc, tx);
            updateWithTcRule(account, true, tx);
            updateCreatedAt(account, props.CLEANUP_INACTIVE_ACCOUNT_FIRST_WARN_DURATION.get(), tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi, never()).resetCleanUpStatus(any(), any());
        verify(uamApi).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertEquals(1, summary.getUsersSentFirstWarning());
            assertZero(summary.getUsersSentSecondWarning());
            assertZero(summary.getUsersReactivatedAfterFirstWarning());
            assertZero(summary.getUsersReactivatedAfterSecondWarning());
            assertZero(summary.getUsersCleanedUp());
            assertZero(summary.getTotalScExpired());
            assertZero(summary.getTotalGcExpired());
        });
    }

    @Test
    void userHasNoGameplayAndHasRecentLastActivity_noNeedToSendEmailOrCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            createAccount(tx);
            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void userPlayedRecently_noNeedToSendEmailOrCleanupBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(tx);
            updateLastGameplay(account, props.CLEANUP_INACTIVE_ACCOUNT_FIRST_WARN_DURATION.get().minusDays(1), tx);
            createAccount(tx);
            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void userWithRecentTcPlayedLongAgoAndPlentyOfTimeElapsedSinceTheSecondWarning_noNeedToSendEmailButShouldCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithTcRule(tx);
            updateLastGameplay(account, tx);
            populateAccountCleanupStatus(account, CleanUpStatusSpec.SECOND_EMAIL_NOTIFY, props.CLEANUP_INACTIVE_ACCOUNT_CLEAN_BALANCE_DELAY.get(), tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi, never()).resetCleanUpStatus(any(), any());
        verify(uamApi, never()).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi).cleanUpAccountBalance(any(), any());
        verify(paymentApi).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertZero(summary.getUsersSentFirstWarning());
            assertZero(summary.getUsersSentSecondWarning());
            assertZero(summary.getUsersReactivatedAfterFirstWarning());
            assertZero(summary.getUsersReactivatedAfterSecondWarning());
            assertEquals(1, summary.getUsersCleanedUp());
            assertDecimalEquals(BigDecimal.TEN, summary.getTotalScExpired());
            assertDecimalEquals(BigDecimal.TEN, summary.getTotalGcExpired());
        });
    }

    @Test
    void userWithOldTcPlayedLongAgoAndEnoughTimeForOldTcElapsedSinceTheSecondWarning_noNeedToSendEmailButShouldCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(tx);
            updateWithTcRule(account, tx);
            updateLastGameplay(account, tx);
            populateAccountCleanupStatus(account, CleanUpStatusSpec.SECOND_EMAIL_NOTIFY, props.CLEANUP_INACTIVE_ACCOUNT_CLEAN_BALANCE_DELAY_WITH_OLD_TC.get(),
                    tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi, never()).resetCleanUpStatus(any(), any());
        verify(uamApi, never()).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi).cleanUpAccountBalance(any(), any());
        verify(paymentApi).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertZero(summary.getUsersSentFirstWarning());
            assertZero(summary.getUsersSentSecondWarning());
            assertZero(summary.getUsersReactivatedAfterFirstWarning());
            assertZero(summary.getUsersReactivatedAfterSecondWarning());
            assertEquals(1, summary.getUsersCleanedUp());
            assertDecimalEquals(BigDecimal.TEN, summary.getTotalScExpired());
            assertDecimalEquals(BigDecimal.TEN, summary.getTotalGcExpired());
        });
    }

    @Test
    void userWithOldTcPlayedLongAgoAndEnoughTimeElapsedSinceTheFirstWarning_sendEmailButDoNotCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(tx);

            updateWithTcRule(account, tx);
            updateLastGameplay(account, tx);
            populateAccountCleanupStatus(account, CleanUpStatusSpec.FIRST_EMAIL_NOTIFY,
                    props.CLEANUP_INACTIVE_ACCOUNT_DELAY_BETWEEN_FIRST_AND_SECOND_WARN_WITH_OLD_TC.get(), tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi, never()).resetCleanUpStatus(any(), any());
        verify(uamApi).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertZero(summary.getUsersSentFirstWarning());
            assertEquals(1, summary.getUsersSentSecondWarning());
            assertZero(summary.getUsersReactivatedAfterFirstWarning());
            assertZero(summary.getUsersReactivatedAfterSecondWarning());
            assertZero(summary.getUsersCleanedUp());
            assertZero(summary.getTotalScExpired());
            assertZero(summary.getTotalGcExpired());
        });
    }

    @Test
    void userWithOldTcPlayedLongAgoAndNotEnoughTimeElapsedSinceTheFirstWarning_shouldNotSendEmailOrCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(tx);

            updateWithTcRule(account, tx);
            updateLastGameplay(account, tx);
            populateAccountCleanupStatus(
                    account,
                    CleanUpStatusSpec.FIRST_EMAIL_NOTIFY,
                    props.CLEANUP_INACTIVE_ACCOUNT_DELAY_BETWEEN_FIRST_AND_SECOND_WARN_WITH_OLD_TC.get().minusMinutes(1),
                    tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void userWithOldTcPlayedLongAgoButNotEnoughTimeForOldTcElapsedSinceTheSecondWarning_shouldNotSendEmailOrCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(tx);

            updateWithTcRule(account, tx);
            updateLastGameplay(account, tx);
            populateAccountCleanupStatus(account, CleanUpStatusSpec.SECOND_EMAIL_NOTIFY, props.CLEANUP_INACTIVE_ACCOUNT_CLEAN_BALANCE_DELAY.get(), tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void userResumedGameActivityAfterFirstWarning_resetCleanupStatusButDoNotSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithTcRule(tx);

            populateAccountCleanupStatusWithoutDate(account, CleanUpStatusSpec.FIRST_EMAIL_NOTIFY, tx);
            updateLastGameplay(account, Duration.ZERO, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi).resetCleanUpStatus(any(), any());
        verify(uamApi, never()).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertZero(summary.getUsersSentFirstWarning());
            assertZero(summary.getUsersSentSecondWarning());
            assertEquals(1, summary.getUsersReactivatedAfterFirstWarning());
            assertZero(summary.getUsersReactivatedAfterSecondWarning());
            assertZero(summary.getUsersCleanedUp());
            assertZero(summary.getTotalScExpired());
            assertZero(summary.getTotalGcExpired());
        });
    }

    @Test
    void userResumedGameActivityAfterSecondWarning_resetCleanupStatusButDoNotSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(tx);

            updateWithTcRule(account, tx);
            populateAccountCleanupStatusWithoutDate(account, CleanUpStatusSpec.SECOND_EMAIL_NOTIFY, tx);
            updateLastGameplay(account, Duration.ZERO, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi).resetCleanUpStatus(any(), any());
        verify(uamApi, never()).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertZero(summary.getUsersSentFirstWarning());
            assertZero(summary.getUsersSentSecondWarning());
            assertZero(summary.getUsersReactivatedAfterFirstWarning());
            assertEquals(1, summary.getUsersReactivatedAfterSecondWarning());
            assertZero(summary.getUsersCleanedUp());
            assertZero(summary.getTotalScExpired());
            assertZero(summary.getTotalGcExpired());
        });
    }

    @Test
    void userResumedGameActivityAfterBalanceCleanup_resetCleanupStatusButDoNotSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(tx);

            updateWithTcRule(account, tx);
            populateAccountCleanupStatusWithoutDate(account, CleanUpStatusSpec.CLEAN_UP_ACCOUNT, tx);
            updateLastGameplay(account, Duration.ZERO, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi, atLeastOnce()).resetCleanUpStatus(any(), any());
        verify(uamApi, never()).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertNoCleanUpJobSummary();
    }

    @Test
    void userResumedGameActivityAfterCleanUp_resetCleanupStatusButDoNotSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(tx);

            populateAccountCleanupStatusWithoutDate(account, CleanUpStatusSpec.CLEAN_UP_ACCOUNT, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi).resetCleanUpStatus(any(), any());
        verify(uamApi, never()).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertNoCleanUpJobSummary();
    }

    @Test
    void userHasRecentRewards_shouldResetCleanupStatusButNotSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithTcRule(tx);

            populateAccountCleanupStatusWithoutDate(account, CleanUpStatusSpec.FIRST_EMAIL_NOTIFY, tx);
            createBonusReward(account, tx);
            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi).resetCleanUpStatus(any(), any());
        verify(uamApi, never()).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertZero(summary.getUsersSentFirstWarning());
            assertZero(summary.getUsersSentSecondWarning());
            assertEquals(1, summary.getUsersReactivatedAfterFirstWarning());
            assertZero(summary.getUsersReactivatedAfterSecondWarning());
            assertZero(summary.getUsersCleanedUp());
            assertZero(summary.getTotalScExpired());
            assertZero(summary.getTotalGcExpired());
        });
    }

    @Test
    void userHasRecentRewardsButNoCleanUpStatus_shouldNotResetCleanupStatusNorSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithTcRule(tx);

            createBonusReward(account, tx);
            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void userHasRecentPaymentOrders_shouldResetCleanupStatusButNotSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithTcRule(tx);

            populateAccountCleanupStatusWithoutDate(account, CleanUpStatusSpec.FIRST_EMAIL_NOTIFY, tx);
            createPaymentOrder(account, tx);
            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi).resetCleanUpStatus(any(), any());
        verify(uamApi, never()).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertZero(summary.getUsersSentFirstWarning());
            assertZero(summary.getUsersSentSecondWarning());
            assertEquals(1, summary.getUsersReactivatedAfterFirstWarning());
            assertZero(summary.getUsersReactivatedAfterSecondWarning());
            assertZero(summary.getUsersCleanedUp());
            assertZero(summary.getTotalScExpired());
            assertZero(summary.getTotalGcExpired());
        });
    }

    @Test
    void userHasRecentWithdrawsButNoCleanUpStatus_shouldNotResetCleanupStatusNorSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithTcRule(tx);

            createWithdraw(account, tx);
            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void userHasRecentWithdraws_shouldResetCleanupStatusButNotSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithTcRule(tx);
            updateLastGameplay(account, tx);
            populateAccountCleanupStatusWithoutDate(account, CleanUpStatusSpec.FIRST_EMAIL_NOTIFY, tx);

            createWithdraw(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verify(uamApi).resetCleanUpStatus(any(), any());
        verify(uamApi, never()).sendCleanUpEmailInBackground(any(), any());
        verify(uamApi, never()).cleanUpAccountBalance(any(), any());
        verify(paymentApi, never()).cleanUpInactiveAccountInBackground(any(), any());

        assertCleanUpJobSummary(summary -> {
            assertZero(summary.getUsersSentFirstWarning());
            assertZero(summary.getUsersSentSecondWarning());
            assertEquals(1, summary.getUsersReactivatedAfterFirstWarning());
            assertZero(summary.getUsersReactivatedAfterSecondWarning());
            assertZero(summary.getUsersCleanedUp());
            assertZero(summary.getTotalScExpired());
            assertZero(summary.getTotalGcExpired());
        });
    }

    @Test
    void userHasRecentPaymentOrdersButNoCleanUpStatus_shouldNotResetCleanupStatusNorSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithTcRule(tx);
            updateLastGameplay(account, tx);

            createPaymentOrder(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void userHasZeroBalance_shouldNotResetCleanupStatusNorSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccount(BigDecimal.ZERO, BigDecimal.ZERO, tx);

            updateWithTcRule(account, true, tx);
            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void userHasNoAcceptedLegalRules_shouldNotResetCleanupStatusNorSendEmailsNorCleanUpBalance() throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithoutLegalRules(tx);
            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @EnumSource(value = CleanUpStatusSpec.class, names = {
            "FIRST_EMAIL_NOTIFY",
            "SECOND_EMAIL_NOTIFY"
    })
    @ParameterizedTest
    void userHasNoLastCleanupEmailSentProperty_shouldConsiderLastActivityDate(CleanUpStatusSpec status) throws Throwable {
        try (var tx = ebean.newTransaction()) {
            Account account = createAccountWithTcRule(tx);

            populateAccountCleanupStatusWithoutDate(account, status, tx);
            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void exceptionDuringJobExecution_shouldSaveFailedSummary() throws Throwable {
        when(uamApi.sendCleanUpEmailInBackground(any(), any())).thenThrow(RuntimeException.class);

        try (var tx = ebean.newTransaction()) {

            Account account = createAccountWithTcRule(tx);

            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        assertNoCleanUpJobSummary();
    }

    @Test
    void brandIsDisabled_shouldNotProcessAccountsOfThisBrand() throws Throwable {
        cfg.setLocalProperty("cleanup-inactive-accounts.disabled-brands", TEST_BRAND);

        try (var tx = ebean.newTransaction()) {

            Account account = createAccountWithTcRule(tx);

            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    @Test
    void abTestingIsDisabled_shouldNotProcessAccounts() throws Throwable {
        cfg.setLocalProperty(props.CLEANUP_INACTIVE_ACCOUNT_AB_TESTING_PERCENTAGE.getKey(), 0);

        try (var tx = ebean.newTransaction()) {

            Account account = createAccountWithTcRule(tx);

            updateLastGameplay(account, tx);

            tx.commit();
        }

        job.runOnceBlocking();

        verifyNoInteractions(uamApi, paymentApi);

        assertNoCleanUpJobSummary();
    }

    private Account createAccount(Transaction tx) throws Throwable {
        Brand brand = ebean.uamBrandRepo().requiredBrandByName(TEST_BRAND, tx);
        return createAccount(brand, BigDecimal.TEN, BigDecimal.TEN, tx);
    }

    private Account createAccountWithTcRule(Transaction tx) throws Throwable {
        Account account = createAccount(tx);
        updateWithTcRule(account, true, tx);

        return account;
    }

    private Account createAccountForNewBrand(Transaction tx) throws Throwable {
        Brand brand = WorkerBrandDataGenerator.genBrand(ebean, TEST_BRAND_2, tx);
        Account account = createAccount(brand, BigDecimal.TEN, BigDecimal.TEN, tx);
        updateWithTcRule(account, true, tx);

        return account;
    }

    private Account createAccount(BigDecimal sc, BigDecimal gc, Transaction tx) throws Throwable {
        Brand brand = ebean.uamBrandRepo().requiredBrandByName(TEST_BRAND, tx);
        return createAccount(brand, sc, gc, tx);
    }

    private Account createAccount(Brand brand, BigDecimal sc, BigDecimal gc, Transaction tx) throws Throwable {
        Account account = DataGenerationService.genAccount(ebean, tx);
        account.setBrand(brand);

        AccountBalance scBalance = createBalance(account, account.getBrand().getSweepstakeCurrency(), sc);
        AccountBalance gcBalance = createBalance(account, account.getBrand().getGoldCurrency(), gc);
        AccountBalance usdBalance = createBalance(account, "USD", BigDecimal.TEN);
        ebean.save(scBalance, tx);
        ebean.save(gcBalance, tx);
        ebean.save(usdBalance, tx);
        ebean.save(account, tx);

        return account;
    }

    private Account createAccountWithoutLegalRules(Transaction tx) throws Throwable {
        Account account = DataGenerationService.genAccount(ebean, tx);
        ebean.truncate(AccountLegalRule.class);

        return account;
    }

    private static AccountBalance createBalance(Account account, String currency, BigDecimal amount) {
        AccountBalance balance = account.accountBalance(currency);
        balance.setAmount(amount);

        return balance;
    }

    private void createBonusReward(Account account, Transaction tx) throws Throwable {
        BonusReward reward = new BonusReward();
        reward.setAccount(account);
        reward.setCode("code");
        reward.setProduct(getProduct(tx));
        reward.setAt(LocalDate.now());
        ebean.save(reward, tx);
    }

    private ImmutableProduct getProduct(Transaction tx) throws Throwable {
        Brand brand = ebean.uamBrandRepo().requiredBrandByName(TEST_BRAND, tx);
        return ebean.uamBrandRepo().requiredProductByCode(brand, InternalProductSpec.CASHBACK.code(), tx);
    }

    private void createPaymentOrder(Account account, Transaction tx) throws Throwable {
        OfferTemplate template = WorkerOfferDataGenerator.getOffer(ebean, tx);
        DataGenerationService.genSuccessfulOrder(ebean, account, template, PaymentProvider.SKRILL, tx);
    }

    private void createWithdraw(Account account, Transaction tx) throws Throwable {
        DataGenerationService.genSuccessfulWithdraw(ebean, account, tx);
    }

    private void populateAccountCleanupStatus(Account account, CleanUpStatusSpec status, Duration timeElapsedSinceLastEmail, Transaction tx) {
        AccountMetaInfo accountMetaInfo = account.getMetaInfo();
        accountMetaInfo.setCleanUpStatus(status);
        accountMetaInfo.setLastCleanupEmailSent(Date.from(Instant.now().minus(timeElapsedSinceLastEmail)));
        ebean.save(accountMetaInfo, tx);
    }

    private void populateAccountCleanupStatusWithoutDate(Account account, CleanUpStatusSpec status, Transaction tx) {
        AccountMetaInfo accountMetaInfo = account.getMetaInfo();
        accountMetaInfo.setCleanUpStatus(status);
        accountMetaInfo.setLastCleanupEmailSent(null);
        ebean.save(accountMetaInfo, tx);
    }

    private void updateLastGameplay(Account account, Transaction tx) {
        updateLastGameplay(account, props.CLEANUP_INACTIVE_ACCOUNT_FIRST_WARN_DURATION.get(), tx);
    }

    private void updateLastGameplay(Account account, Duration timeElapsedSinceLastGameplay, Transaction tx) {
        account.getGameplayInfo().setLastGameplay(Date.from(Instant.now().minus(timeElapsedSinceLastGameplay).minusMillis(1)));
        ebean.save(account, tx);
    }

    private void updateWithTcRule(Account account, Transaction tx) {
        updateWithTcRule(account, false, tx);
    }

    private void updateWithTcRule(Account account, boolean recent, Transaction tx) {
        LegalRule rule = new LegalRule();
        rule.setRuleVersion(recent ? CLEAN_UP_TC_VERSION : CLEAN_UP_TC_VERSION - 1);
        rule.setBrand(account.getBrand());
        rule.setType(RuleTypeSpec.TC);
        rule.setSetting(LegalRuleSettingSpec.values()[ApiFactory.RANDOM.nextInt(LegalRuleSettingSpec.values().length)]);
        rule.setVersionDate(PlatformUtil.toLocalUTCDate());
        rule.setGoldOnly(ApiFactory.RANDOM.nextBoolean());
        rule.setCountry(PlatformUtil.randomAlphabetic(2));
        ebean.save(rule, tx);

        AccountLegalRule accountLegalRule = new AccountLegalRule(account, rule);
        ebean.save(accountLegalRule, tx);
    }

    private void updateCreatedAt(Account account, Duration timeElapsedSinceAccountCreated, Transaction tx) {
        account.setCreatedAt(Date.from(Instant.now().minus(timeElapsedSinceAccountCreated)));
        ebean.save(account, tx);
    }

    private void mockServicesApi() throws Throwable {
        mockUtil.mockServiceCall(uamApi,
                mockService -> mockService.resetCleanUpStatus(any(), any()),
                ResetCleanUpStatusResponse.getDefaultInstance());

        mockUtil.mockServiceCall(uamApi,
                mockService -> mockService.sendCleanUpEmailInBackground(any(), any()),
                CleanUpInactiveAccountNotificationResponse.getDefaultInstance());

        mockUtil.mockServiceCall(uamApi,
                mockService -> mockService.cleanUpAccountBalance(any(), any()),
                CleanUpAccountBalanceResponse.getDefaultInstance());

        mockUtil.mockServiceCall(paymentApi,
                mockService -> mockService.cleanUpInactiveAccountInBackground(any(), any()),
                CleanUpInactiveAccountResponse.getDefaultInstance());
    }

    private void assertNoCleanUpJobSummary() {
        boolean exists = ebean.find(CleanUpJobSummary.class).exists();
        assertFalse(exists);
    }

    @SneakyThrows
    private void assertCleanUpJobSummary(Consumer<CleanUpJobSummary> assertions) {
        await()
                .atMost(5, TimeUnit.SECONDS)
                .pollInterval(1, TimeUnit.SECONDS)
                .untilAsserted(() -> {
                    CleanUpJobSummary summary = ebean.find(CleanUpJobSummary.class).findOne();
                    assertNotNull(summary);
                    assertTrue(summary.getOk());
                    assertNotNull(summary.getId());
                    assertNotNull(summary.getStartedAt());
                    assertNotNull(summary.getEndedAt());

                    try (Transaction tx = ebean.newReadOnlyTransaction()) {
                        Brand brand = ebean.uamBrandRepo().requiredBrandByName(TEST_BRAND, tx);
                        assertEquals(brand.getId(), summary.getBrandId());
                    }

                    assertEquals(1, summary.getTotalUsersProcessed());

                    assertions.accept(summary);
                });
    }

    private static void assertCleanUpJobSummary(CleanUpJobSummary summary, int brandId, long usersSentFirstWarn) {
        assertEquals(brandId, summary.getBrandId());
        assertEquals(usersSentFirstWarn, summary.getUsersSentFirstWarning());
        assertEquals(usersSentFirstWarn, summary.getTotalUsersProcessed());
    }

    private static void assertZero(Long value) {
        assertEquals(0, value);
    }

    private static void assertZero(BigDecimal value) {
        assertDecimalEquals(BigDecimal.ZERO, value);
    }

    private static void assertDecimalEquals(BigDecimal expected, BigDecimal actual) {
        assertEquals(0, expected.compareTo(actual));
    }
}
