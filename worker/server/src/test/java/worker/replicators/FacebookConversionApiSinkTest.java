package worker.replicators;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.validateMockitoUsage;
import static org.mockito.Mockito.verify;

import java.time.Period;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.facebook.ads.sdk.APIContext;
import com.facebook.ads.sdk.serverside.Event;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import ads.facebook.FacebookEventPublishContext;
import ads.facebook.sink.PurchaseFacebookConversionReplicator;
import ads.facebook.sink.SignUpFacebookConversionReplicator;
import ads.facebook.sink.SinkReplicator;
import api.v1.PlatformSpec;
import framework.listeners.WorkerBrandDataGenerator;
import framework.util.DataGenerationService;
import io.ebean.Transaction;
import payment.api.v1.PurchaseSuccessEvent;
import payment.api.v1.internal.AccountPaymentInfo;
import payment.api.v1.internal.OrderInfo;
import payment.api.v1.internal.PaymentAccountInfo;
import uam.api.v1.internal.AccountCreateEvent;
import uam.api.v1.internal.AccountInfo;
import uam.model.Account;
import uam.model.Citizen;
import uam.model.Person;
import worker.WorkerEbeanJpaManager;
import worker.WorkerMasterEbeanJpaManager;
import worker.WorkerProto;
import worker.WorkerSpringBootTestContextBootstrapper;
import worker.di.CommonWorkerDiModule;
import worker.di.FacebookMockDiModule;
import worker.di.MockWorkerDatabaseDiModule;
import worker.service.extractor.AccountIdExtractorChain;

@ExtendWith(SpringExtension.class)
@BootstrapWith(WorkerSpringBootTestContextBootstrapper.class)
@ContextConfiguration(
        classes = {
                MockWorkerDatabaseDiModule.class,
                CommonWorkerDiModule.class,
                FacebookMockDiModule.class
        })
@TestExecutionListeners(mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS, listeners = { WorkerBrandDataGenerator.class })
class FacebookConversionApiSinkTest {
    @Autowired
    private DynamicCloud cloud;

    @Autowired
    private WorkerMasterEbeanJpaManager ebean;

    @SpyBean
    private SignUpFacebookConversionReplicator signUpFacebookConversionReplicator;

    @SpyBean
    private PurchaseFacebookConversionReplicator purchaseFacebookConversionReplicator;

    @SpyBean
    private AccountIdExtractorChain accountIdExtractorChain;

    @Captor
    private ArgumentCaptor<List<Event>> eventsCaptor;

    @Captor
    private ArgumentCaptor<List<Event>> eventsCaptor2;

    private PlainServiceInfo si;

    @BeforeEach
    public void beforeEach() {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FACEBOOK_CONVERSION, "https://api_key:<EMAIL>?ad1=1&ad2=2"));
        si = UPSs.findScopedRequiredServiceInfoByName(WorkerBrandDataGenerator.BRAND, cloud, WorkerProto.UPS_FACEBOOK_CONVERSION);
    }

    @AfterEach
    public void afterEach() {
        validateMockitoUsage();
    }

    @Test
    void testRegisterTwoAdAccounts() throws Throwable {
        doNothing().when(signUpFacebookConversionReplicator).sendRequestByAdAccount(any(), any(), any(), any());

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        signUpFacebookConversionReplicator.apply(toAccountCreateEvent(account), true, 30, null, Period.ofDays(90));

        ArgumentCaptor<APIContext> captor = ArgumentCaptor.forClass(APIContext.class);

        verify(signUpFacebookConversionReplicator, times(2)).sendRequestByAdAccount(any(), argThat(l -> l.size() == 1), captor.capture(), any());

        var captured = captor.getAllValues();

        var ad1 = UPSs.getRequiredQueryParam(SinkReplicator.AdAccount.AD1.code(), si);
        var ad2 = UPSs.getRequiredQueryParam(SinkReplicator.AdAccount.AD2.code(), si);

        Assertions.assertEquals(2, captured.size());

        Assertions.assertTrue(captured.stream().anyMatch(e -> e.getAccessToken().equals(ad1)));
        Assertions.assertTrue(captured.stream().anyMatch(e -> e.getAccessToken().equals(ad2)));

        reset(signUpFacebookConversionReplicator);
    }

    @Test
    void shouldTakeFacebookConversionOnTheFly() throws Throwable {
        doNothing().when(signUpFacebookConversionReplicator).sendRequestByAdAccount(any(), any(), any(), any());

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FACEBOOK_CONVERSION, "https://changed_api_key:<EMAIL>?ad1=new1&ad2=new2"));

        signUpFacebookConversionReplicator.apply(toAccountCreateEvent(account), true, 30, null, Period.ofDays(90));

        ArgumentCaptor<APIContext> captor = ArgumentCaptor.forClass(APIContext.class);

        verify(signUpFacebookConversionReplicator, times(2)).sendRequestByAdAccount(any(), argThat(l -> l.size() == 1), captor.capture(), any());

        var captured = captor.getAllValues();

        Assertions.assertEquals(2, captured.size());

        Assertions.assertTrue(captured.stream().anyMatch(e -> e.getAccessToken().equals("new1")));
        Assertions.assertTrue(captured.stream().anyMatch(e -> e.getAccessToken().equals("new2")));
        reset(signUpFacebookConversionReplicator);
    }

    @Test
    void testRegisterAdAccountsWhenDeleted() throws Throwable {
        doNothing().when(signUpFacebookConversionReplicator).sendRequestByAdAccount(any(), any(), any(), any());

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }
        deleteAccount(account);

        signUpFacebookConversionReplicator.apply(toAccountCreateEvent(account), true, 30, null, Period.ofDays(90));

        ArgumentCaptor<APIContext> captor = ArgumentCaptor.forClass(APIContext.class);

        verify(signUpFacebookConversionReplicator, never()).sendRequestByAdAccount(any(), argThat(l -> l.size() == 1), captor.capture(), any());

        var captured = captor.getAllValues();
        Assertions.assertEquals(0, captured.size());

        reset(signUpFacebookConversionReplicator);
    }

    private static AccountCreateEvent toAccountCreateEvent(Account account) {
        var accountInfo = AccountInfo.newBuilder()
                .setId(account.getId())
                .setBrandName(WorkerBrandDataGenerator.BRAND)
                .build();

        return AccountCreateEvent.newBuilder()
                .setAt(new Date().getTime())
                .setAccount(accountInfo)
                .build();
    }

    @Test
    void testPurchaseFirstDeposit() throws Throwable {
        doNothing().when(purchaseFacebookConversionReplicator).sendRequestByAdAccount(any(), any(), any(), any());

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        // first deposit event
        var e1 = PurchaseSuccessEvent.newBuilder()
                .setOrderInfo(OrderInfo.newBuilder()
                        .setAt(new Date().getTime())
                        .setAmount("13.37")
                        .setPlatform(PlatformSpec.WEB.code())
                        .build())
                .setAccountPaymentInfo(AccountPaymentInfo.newBuilder()
                        .setTotalPurchaseAmountStr("13.37")
                        .setIsFirstPurchase(true)
                        .build())
                .setPaymentAccountInfo(PaymentAccountInfo.newBuilder().setAccountId(account.getId()).setBrandName(WorkerBrandDataGenerator.BRAND).build());
        purchaseFacebookConversionReplicator.apply(e1.build(), mock(FacebookEventPublishContext.class), null, Period.ofDays(90));

        verify(purchaseFacebookConversionReplicator, times(2))
                .sendRequestByAdAccount(any(), eventsCaptor.capture(), any(), any());
        assertSingleEventOfTypeSent(SinkReplicator.ConversionEvent.FIRST_PURCHASE_EVENT, eventsCaptor.getAllValues());
        assertSingleEventOfTypeSent(SinkReplicator.ConversionEvent.QUALIFIED_FIRST_PURCHASE_EVENT, eventsCaptor.getAllValues());

        reset(purchaseFacebookConversionReplicator);
        doNothing().when(purchaseFacebookConversionReplicator).sendRequestByAdAccount(any(), any(), any(), any());

        // another purchase
        var e2 = PurchaseSuccessEvent.newBuilder()
                .setOrderInfo(OrderInfo.newBuilder()
                        .setAt(new Date().getTime())
                        .setAmount("13.37")
                        .setPlatform(PlatformSpec.WEB.code())
                        .build())
                .setAccountPaymentInfo(AccountPaymentInfo.newBuilder()
                        .setTotalPurchaseAmountStr("13.37")
                        .setIsFirstPurchase(false)
                        .build())
                .setPaymentAccountInfo(PaymentAccountInfo.newBuilder().setAccountId(account.getId()).setBrandName(WorkerBrandDataGenerator.BRAND).build());

        purchaseFacebookConversionReplicator.apply(e2.build(), mock(FacebookEventPublishContext.class), null, Period.ofDays(90));

        verify(purchaseFacebookConversionReplicator, times(2)).sendRequestByAdAccount(any(), eventsCaptor2.capture(), any(), any());
        Assertions.assertTrue(eventsCaptor2.getAllValues().stream().allMatch(
                l -> l.stream().noneMatch(e -> e.getEventName().equalsIgnoreCase(SinkReplicator.ConversionEvent.FIRST_PURCHASE_EVENT.code()))));

        reset(purchaseFacebookConversionReplicator);
    }

    @Test
    void testPurchaseTwoAdAccounts() throws Throwable {
        doNothing().when(purchaseFacebookConversionReplicator).sendRequestByAdAccount(any(), any(), any(), any());

        purchaseFacebookConversionReplicator.apply(getPurchaseEvent(), mock(FacebookEventPublishContext.class), null, Period.ofDays(90));

        ArgumentCaptor<APIContext> captor = ArgumentCaptor.forClass(APIContext.class);

        verify(purchaseFacebookConversionReplicator, times(2))
                .sendRequestByAdAccount(any(), argThat(l -> l.size() == 1), captor.capture(), any());

        Assertions.assertEquals(2, captor.getAllValues().size());

        var captured = captor.getAllValues();

        var ad1 = UPSs.getRequiredQueryParam(SinkReplicator.AdAccount.AD1.code(), si);
        var ad2 = UPSs.getRequiredQueryParam(SinkReplicator.AdAccount.AD2.code(), si);

        Assertions.assertEquals(2, captured.size());

        Assertions.assertTrue(captured.stream().anyMatch(e -> e.getAccessToken().equals(ad1)));
        Assertions.assertTrue(captured.stream().anyMatch(e -> e.getAccessToken().equals(ad2)));

        reset(purchaseFacebookConversionReplicator);
    }

    @Test
    void testDoNotSendEventOnNOTValidPostalCode() throws Throwable {
        doNothing().when(purchaseFacebookConversionReplicator).sendRequestByAdAccount(any(), any(), any(), any());

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            setPostalCodeToAccount(account, "k", tx);
            tx.commit();
        }


        // first deposit event
        var e1 = PurchaseSuccessEvent.newBuilder()
                .setOrderInfo(OrderInfo.newBuilder()
                        .setAt(new Date().getTime())
                        .setAmount("13.37")
                        .setPlatform(PlatformSpec.WEB.code())
                        .build())
                .setAccountPaymentInfo(AccountPaymentInfo.newBuilder()
                        .setIsFirstPurchase(true)
                        .build())
                .setPaymentAccountInfo(PaymentAccountInfo.newBuilder().setAccountId(account.getId()).setBrandName(WorkerBrandDataGenerator.BRAND).build());

        purchaseFacebookConversionReplicator.apply(e1.build(), mock(FacebookEventPublishContext.class), null, Period.ofDays(90));

        verify(purchaseFacebookConversionReplicator, times(0))
                .sendRequestByAdAccount(any(), eventsCaptor.capture(), any(), any());
        assertSingleEventOfTypeSent(SinkReplicator.ConversionEvent.FIRST_PURCHASE_EVENT, eventsCaptor.getAllValues());
    }

    @Test
    void testDoNotSendEventOnAccountDeleted() throws Throwable {
        doNothing().when(purchaseFacebookConversionReplicator).sendRequestByAdAccount(any(), any(), any(), any());

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }
        deleteAccount(account);

        // first deposit event
        var e1 = PurchaseSuccessEvent.newBuilder()
                .setOrderInfo(OrderInfo.newBuilder()
                        .setAt(new Date().getTime())
                        .setAmount("13.37")
                        .setPlatform(PlatformSpec.WEB.code())
                        .build())
                .setAccountPaymentInfo(AccountPaymentInfo.newBuilder()
                        .setIsFirstPurchase(true)
                        .build())
                .setPaymentAccountInfo(PaymentAccountInfo.newBuilder().setAccountId(account.getId()).setBrandName(WorkerBrandDataGenerator.BRAND).build());

        purchaseFacebookConversionReplicator.apply(e1.build(), mock(FacebookEventPublishContext.class), null, Period.ofDays(90));

        verify(purchaseFacebookConversionReplicator, times(0))
                .sendRequestByAdAccount(any(), eventsCaptor.capture(), any(), any());
        assertSingleEventOfTypeSent(SinkReplicator.ConversionEvent.FIRST_PURCHASE_EVENT, eventsCaptor.getAllValues());
    }

    @Test
    void testSendEventOnValidPostalCode() throws Throwable {
        doNothing().when(purchaseFacebookConversionReplicator).sendRequestByAdAccount(any(), any(), any(), any());

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            setPostalCodeToAccount(account, "12345-234", tx);
            tx.commit();
        }


        // first deposit event
        var e1 = PurchaseSuccessEvent.newBuilder()
                .setOrderInfo(OrderInfo.newBuilder()
                        .setAt(new Date().getTime())
                        .setAmount("13.37")
                        .setPlatform(PlatformSpec.WEB.code())
                        .build())
                .setAccountPaymentInfo(AccountPaymentInfo.newBuilder()
                        .setTotalPurchaseAmountStr("0.0")
                        .setIsFirstPurchase(true)
                        .build())
                .setPaymentAccountInfo(PaymentAccountInfo.newBuilder().setAccountId(account.getId()).setBrandName(WorkerBrandDataGenerator.BRAND).build());

        purchaseFacebookConversionReplicator.apply(e1.build(), mock(FacebookEventPublishContext.class), null, Period.ofDays(90));

        verify(purchaseFacebookConversionReplicator, times(2))
                .sendRequestByAdAccount(any(), eventsCaptor.capture(), any(), any());

        assertSingleEventOfTypeSent(SinkReplicator.ConversionEvent.FIRST_PURCHASE_EVENT, eventsCaptor.getAllValues());
    }

    private static void assertSingleEventOfTypeSent(SinkReplicator.ConversionEvent type, List<List<Event>> events) {
        Assertions.assertTrue(events.stream().allMatch(
                l -> l.stream().filter(e -> e.getEventName().equalsIgnoreCase(type.code())).count() == 1));
    }

    private void setPostalCodeToAccount(Account account, String k, Transaction tx) throws Throwable {
        Person person = new Person();
        person.setFirstName("First");
        person.setLastName("Last");
        ebean.save(person, tx);
        account.setPerson(person);
        ebean.save(account, tx);
        Citizen citizen = new Citizen();
        citizen.setPostal(k);
        citizen.setPerson(account.getPerson());
        citizen.setCountry(account.getCountry());
        ebean.save(citizen, tx);
    }

    private PurchaseSuccessEvent getPurchaseEvent() throws Throwable {
        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        return PurchaseSuccessEvent.newBuilder()
                .setOrderInfo(OrderInfo.newBuilder()
                        .setAt(new Date().getTime())
                        .setAmount("2.00")
                        .setPlatform(PlatformSpec.WEB.code())
                        .build())
                .setAccountPaymentInfo(AccountPaymentInfo.newBuilder()
                        .setTotalPurchaseAmountStr("13.37")
                        .setIsFirstPurchase(false)
                        .build())
                .setPaymentAccountInfo(PaymentAccountInfo.newBuilder().setAccountId(account.getId()).setBrandName(WorkerBrandDataGenerator.BRAND).build())
                .build();
    }

    private void deleteAccount(Account account) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            account.setDeleted(true);
            ebean.save(account, tx);
            tx.commit();
        }
    }
}
