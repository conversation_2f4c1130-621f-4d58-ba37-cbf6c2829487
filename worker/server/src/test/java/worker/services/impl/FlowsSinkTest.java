package worker.services.impl;

import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static uam.api.v1.PaymentProvider.SPREEDLY_FISERV;

import java.math.BigDecimal;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ups.PlainServiceInfo;

import api.v1.LocationInfo;
import api.v1.PlatformSpec;
import bi.flows.FlowsJaxRsClient;
import bi.flows.data.AccountFieldUpdateFlowsEvent;
import bi.flows.data.EmailVerificationFlowsEvent;
import bi.flows.data.FlowsEvent;
import bi.flows.data.LowBalanceFlowsEvent;
import bi.flows.data.PhoneVerificationFlowsEvent;
import bi.flows.data.PurchaseFlowsEvent;
import bi.flows.data.RedeemConfirmFlowsEvent;
import bi.flows.data.SignInFlowsEvent;
import bi.flows.data.SignUpFlowsEvent;
import bi.flows.sink.AccountFieldUpdateFlowsReplicator;
import bi.flows.sink.EmailVerificationFlowsReplicator;
import bi.flows.sink.LowBalanceFlowsReplicator;
import bi.flows.sink.PhoneNumberVerificationFlowsReplicator;
import bi.flows.sink.PurchaseFlowsReplicator;
import bi.flows.sink.RedeemConfirmFlowsReplicator;
import bi.flows.sink.SignInFlowsReplicator;
import bi.flows.sink.SignUpFlowsReplicator;
import framework.listeners.WorkerBrandDataGenerator;
import framework.listeners.WorkerOfferDataGenerator;
import framework.listeners.WorkerPaymentProviderDataGenerator;
import framework.util.DataGenerationService;
import fraud.api.v1.PhoneNumberVerificationEvent;
import payment.api.v1.PaymentOrderCompletedEvent;
import payment.api.v1.RedeemConfirmEvent;
import payment.model.OfferTemplate;
import payment.model.PaymentOrder;
import uam.api.v1.RedeemMoneyInfo;
import uam.api.v1.internal.AccountCreateEvent;
import uam.api.v1.internal.AccountField;
import uam.api.v1.internal.AccountFieldUpdateEvent;
import uam.api.v1.internal.AccountInfo;
import uam.api.v1.internal.AccountLowBalanceEvent;
import uam.api.v1.internal.AccountPaymentRoutingInfo;
import uam.api.v1.internal.AccountSignInEvent;
import uam.api.v1.internal.EmailVerificationEvent;
import uam.model.Account;
import worker.WorkerMasterEbeanJpaManager;
import worker.WorkerProto;
import worker.WorkerSpringBootTestContextBootstrapper;
import worker.di.BloomreachDiModule;
import worker.di.CommonWorkerDiModule;
import worker.di.MockWorkerDatabaseDiModule;

@ExtendWith(SpringExtension.class)
@BootstrapWith(WorkerSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = { MockWorkerDatabaseDiModule.class, BloomreachDiModule.class, CommonWorkerDiModule.class })
@TestExecutionListeners(
        mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS,
        listeners = { WorkerBrandDataGenerator.class, WorkerOfferDataGenerator.class, WorkerPaymentProviderDataGenerator.class })
class FlowsSinkTest {
    private static final String SLASH = "/";

    @Autowired
    private DynamicCloud cloud;

    @Autowired
    private WorkerMasterEbeanJpaManager ebean;

    @SpyBean
    private SignUpFlowsReplicator signUpReplicator;

    @SpyBean
    private SignInFlowsReplicator signInReplicator;

    @SpyBean
    private EmailVerificationFlowsReplicator emailVerificationReplicator;

    @SpyBean
    private PhoneNumberVerificationFlowsReplicator phoneNumberVerificationReplicator;

    @SpyBean
    private LowBalanceFlowsReplicator lowBalanceReplicator;

    @SpyBean
    private AccountFieldUpdateFlowsReplicator accountFieldUpdateReplicator;

    @SpyBean
    private PurchaseFlowsReplicator purchaseReplicator;

    @SpyBean
    private RedeemConfirmFlowsReplicator redeemReplicator;

    @MockBean
    private FlowsJaxRsClient client;

    @Test
    void testSignUpEvent() throws Throwable {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FLOWS, "https://workspace:<EMAIL>"));

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        var accountInfo = AccountInfo.newBuilder()
                .setBrandName(WorkerBrandDataGenerator.BRAND)
                .setId(account.getId())
                .setRoutingKey(account.getHash());
        var location = LocationInfo.newBuilder()
                .setState("test_state");
        var registerEvent = AccountCreateEvent.newBuilder()
                .setAccount(accountInfo)
                .setLocation(location);

        signUpReplicator.apply(registerEvent.build());

        ArgumentCaptor<FlowsEvent> captor = ArgumentCaptor.forClass(FlowsEvent.class);

        verify(client).sendEventAsync(eq(SignUpFlowsReplicator.EVENT_REGISTRATION), captor.capture(), any());

        FlowsEvent captured = captor.getValue();
        assertInstanceOf(SignUpFlowsEvent.class, captured);

        SignUpFlowsEvent event = (SignUpFlowsEvent) captured;
        Assertions.assertEquals(String.join(SLASH, accountInfo.getRoutingKey(), String.valueOf(accountInfo.getId())), event.accountId);
        Assertions.assertEquals(accountInfo.getEmail(), event.email);
        Assertions.assertEquals(location.getState(), event.state);
    }

    @Test
    void testSignInEvent() throws Throwable {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FLOWS, "https://workspace:<EMAIL>"));

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        var accountInfo = AccountInfo.newBuilder()
                .setBrandName(WorkerBrandDataGenerator.BRAND)
                .setId(account.getId())
                .setRoutingKey(account.getHash());
        var loginEvent = AccountSignInEvent.newBuilder()
                .setAccount(accountInfo);

        signInReplicator.apply(loginEvent.build());

        ArgumentCaptor<FlowsEvent> captor = ArgumentCaptor.forClass(FlowsEvent.class);

        verify(client).sendEventAsync(eq(SignInFlowsReplicator.EVENT_LOGIN), captor.capture(), any());

        FlowsEvent captured = captor.getValue();
        assertInstanceOf(SignInFlowsEvent.class, captured);

        SignInFlowsEvent event = (SignInFlowsEvent) captured;
        Assertions.assertEquals(String.join(SLASH, accountInfo.getRoutingKey(), String.valueOf(accountInfo.getId())), event.accountId);
    }

    @ParameterizedTest
    @ValueSource(booleans = { true, false })
    void testEmailVerificationEvent(boolean verified) throws Throwable {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FLOWS, "https://workspace:<EMAIL>"));

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        var accountInfo = AccountInfo.newBuilder()
                .setBrandName(WorkerBrandDataGenerator.BRAND)
                .setId(account.getId())
                .setEmail(account.getEmail())
                .setRoutingKey(account.getHash());
        var emailVerificationEvent = EmailVerificationEvent.newBuilder()
                .setAccount(accountInfo)
                .setVerified(verified);

        emailVerificationReplicator.apply(emailVerificationEvent.build());

        ArgumentCaptor<FlowsEvent> captor = ArgumentCaptor.forClass(FlowsEvent.class);

        verify(client).sendEventAsync(eq(EmailVerificationFlowsReplicator.EVENT_EMAIL_VERIFICATION), captor.capture(), any());

        FlowsEvent captured = captor.getValue();
        assertInstanceOf(EmailVerificationFlowsEvent.class, captured);

        EmailVerificationFlowsEvent event = (EmailVerificationFlowsEvent) captured;
        Assertions.assertEquals(String.join(SLASH, accountInfo.getRoutingKey(), String.valueOf(accountInfo.getId())), event.accountId);
        Assertions.assertEquals(verified, event.verified);
        Assertions.assertEquals(accountInfo.getEmail(), event.email);
    }

    @Test
    void testPhoneNumberVerificationEvent() throws Throwable {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FLOWS, "https://workspace:<EMAIL>"));

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        String phoneNumber = "+12345";

        var phoneVerificationEvent = PhoneNumberVerificationEvent.newBuilder()
                .setAccountId(account.getId())
                .setHash(account.getHash())
                .setBrandName(WorkerBrandDataGenerator.BRAND)
                .setPhoneNumber(phoneNumber)
                .setVerified(true);

        phoneNumberVerificationReplicator.apply(phoneVerificationEvent.build());

        ArgumentCaptor<FlowsEvent> captor = ArgumentCaptor.forClass(FlowsEvent.class);

        verify(client).sendEventAsync(eq(PhoneNumberVerificationFlowsReplicator.EVENT_PHONE_NUMBER_VERIFICATION), captor.capture(), any());

        FlowsEvent captured = captor.getValue();
        assertInstanceOf(PhoneVerificationFlowsEvent.class, captured);

        PhoneVerificationFlowsEvent event = (PhoneVerificationFlowsEvent) captured;
        Assertions.assertEquals(String.join(SLASH, account.getHash(), String.valueOf(account.getId())), event.accountId);
        Assertions.assertEquals(phoneNumber, event.phoneNumber);
    }

    @Test
    void testLowBalanceEvent() throws Throwable {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FLOWS, "https://workspace:<EMAIL>"));

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        var accountInfo = AccountInfo.newBuilder()
                .setBrandName(WorkerBrandDataGenerator.BRAND)
                .setId(account.getId())
                .setRoutingKey(account.getHash());
        var lowBalanceEvent = AccountLowBalanceEvent.newBuilder().setAccount(accountInfo);

        lowBalanceReplicator.apply(lowBalanceEvent.build());

        ArgumentCaptor<FlowsEvent> captor = ArgumentCaptor.forClass(FlowsEvent.class);

        verify(client).sendEventAsync(eq(LowBalanceFlowsReplicator.EVENT_LOW_BALANCE), captor.capture(), any());

        FlowsEvent captured = captor.getValue();
        assertInstanceOf(LowBalanceFlowsEvent.class, captured);

        LowBalanceFlowsEvent event = (LowBalanceFlowsEvent) captured;
        Assertions.assertEquals(String.join(SLASH, accountInfo.getRoutingKey(), String.valueOf(accountInfo.getId())), event.accountId);
    }

    @Test
    void testAccountFieldUpdateEvent_Status() throws Throwable {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FLOWS, "https://workspace:<EMAIL>"));

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }
        String previous = "previous_status";
        String current = "current_status";

        var accountInfo = AccountInfo.newBuilder()
                .setBrandName(WorkerBrandDataGenerator.BRAND)
                .setId(account.getId())
                .setRoutingKey(account.getHash());

        var accountFieldUpdateEvent = AccountFieldUpdateEvent.newBuilder()
                .setAccount(accountInfo)
                .setField(AccountField.ACCOUNT_STATUS_FIELD)
                .setPrevious(previous)
                .setCurrent(current);

        accountFieldUpdateReplicator.apply(accountFieldUpdateEvent.build());

        ArgumentCaptor<FlowsEvent> captor = ArgumentCaptor.forClass(FlowsEvent.class);

        verify(client).sendEventAsync(eq(AccountFieldUpdateFlowsReplicator.EVENT_ACCOUNT_STATUS_CHANGE), captor.capture(), any());

        FlowsEvent captured = captor.getValue();
        assertInstanceOf(AccountFieldUpdateFlowsEvent.class, captured);

        AccountFieldUpdateFlowsEvent event = (AccountFieldUpdateFlowsEvent) captured;
        Assertions.assertEquals(String.join(SLASH, accountInfo.getRoutingKey(), String.valueOf(accountInfo.getId())), event.accountId);
        Assertions.assertEquals(previous, event.previous);
        Assertions.assertEquals(current, event.current);
    }

    @Test
    void testAccountFieldUpdateEvent_KYC() throws Throwable {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FLOWS, "https://workspace:<EMAIL>"));

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }
        String previous = "previous_kyc";
        String current = "current_kyc";

        var accountInfo = AccountInfo.newBuilder()
                .setBrandName(WorkerBrandDataGenerator.BRAND)
                .setId(account.getId())
                .setRoutingKey(account.getHash());

        var accountFieldUpdateEvent = AccountFieldUpdateEvent.newBuilder()
                .setAccount(accountInfo)
                .setField(AccountField.ACCOUNT_KYC_FIELD)
                .setPrevious(previous)
                .setCurrent(current);

        accountFieldUpdateReplicator.apply(accountFieldUpdateEvent.build());

        ArgumentCaptor<FlowsEvent> captor = ArgumentCaptor.forClass(FlowsEvent.class);

        verify(client).sendEventAsync(eq(AccountFieldUpdateFlowsReplicator.EVENT_ACCOUNT_KYC_CHANGE), captor.capture(), any());

        FlowsEvent captured = captor.getValue();
        assertInstanceOf(AccountFieldUpdateFlowsEvent.class, captured);

        AccountFieldUpdateFlowsEvent event = (AccountFieldUpdateFlowsEvent) captured;
        Assertions.assertEquals(String.join(SLASH, accountInfo.getRoutingKey(), String.valueOf(accountInfo.getId())), event.accountId);
        Assertions.assertEquals(previous, event.previous);
        Assertions.assertEquals(current, event.current);
    }

    @ParameterizedTest
    @ValueSource(booleans = { true, false })
    void testPurchaseEvent(boolean isFirstPurchase) throws Throwable {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FLOWS, "https://workspace:<EMAIL>"));

        Account account;
        PaymentOrder order;
        try (var tx = ebean.newTransaction()) {
            DataGenerationService.genProvider(ebean, tx);
            account = DataGenerationService.genAccount(ebean, tx);
            OfferTemplate offer = WorkerOfferDataGenerator.getOffer(ebean, tx);
            order = DataGenerationService.genOrder(
                    ebean,
                    SPREEDLY_FISERV,
                    account,
                    offer,
                    PlatformSpec.WEB,
                    PlatformUtil.randomAlphabetic(5),
                    PlatformUtil.randomUUID(),
                    false,
                    888.99,
                    999.99,
                    tx);
            tx.commit();
        }

        var accountInfo = AccountPaymentRoutingInfo.newBuilder()
                .setBrand(WorkerBrandDataGenerator.BRAND)
                .setId(account.getId())
                .setHash(account.getHash());

        var purchaseEvent = PaymentOrderCompletedEvent.newBuilder()
                .setPaymentRouting(accountInfo)
                .setPaymentOrderId(order.getId())
                .setAmount(BigDecimal.ONE.toString())
                .setFirstPurchase(isFirstPurchase);

        purchaseReplicator.apply(purchaseEvent.build());

        ArgumentCaptor<FlowsEvent> captor = ArgumentCaptor.forClass(FlowsEvent.class);

        var eventType = purchaseEvent.getFirstPurchase() ? PurchaseFlowsReplicator.EVENT_FIRST_PURCHASE : PurchaseFlowsReplicator.EVENT_PURCHASE;
        verify(client).sendEventAsync(eq(eventType), captor.capture(), any());

        FlowsEvent captured = captor.getValue();
        assertInstanceOf(PurchaseFlowsEvent.class, captured);

        PurchaseFlowsEvent event = (PurchaseFlowsEvent) captured;
        Assertions.assertEquals(String.join(SLASH, accountInfo.getHash(), String.valueOf(accountInfo.getId())), event.accountId);
        Assertions.assertEquals(new BigDecimal(purchaseEvent.getAmount()), event.price);
        Assertions.assertEquals(order.getGcAmount(), event.gcAmount);
        Assertions.assertEquals(order.getScAmount(), event.scAmount);
    }

    @ParameterizedTest
    @ValueSource(booleans = { true, false })
    void testRedeemEvent(boolean isFirstRedeem) throws Throwable {
        cloud.addUps(new PlainServiceInfo(WorkerProto.UPS_FLOWS, "https://workspace:<EMAIL>"));

        Account account;
        try (var tx = ebean.newTransaction()) {
            account = DataGenerationService.genAccount(ebean, tx);
            tx.commit();
        }

        var accountInfo = AccountPaymentRoutingInfo.newBuilder()
                .setBrand(WorkerBrandDataGenerator.BRAND)
                .setId(account.getId())
                .setHash(account.getHash());
        var redeem = RedeemMoneyInfo.newBuilder()
                .setAmount(BigDecimal.TEN.toString())
                .setIsFirstRedeem(isFirstRedeem);
        var redeemEvent = RedeemConfirmEvent.newBuilder()
                .setPaymentRouting(accountInfo)
                .setRedeem(redeem);

        redeemReplicator.apply(redeemEvent.build());

        ArgumentCaptor<FlowsEvent> captor = ArgumentCaptor.forClass(FlowsEvent.class);

        var eventType = redeem.getIsFirstRedeem() ? RedeemConfirmFlowsReplicator.EVENT_FIRST_REDEEM : RedeemConfirmFlowsReplicator.EVENT_REDEEM;
        verify(client).sendEventAsync(eq(eventType), captor.capture(), any());

        FlowsEvent captured = captor.getValue();
        assertInstanceOf(RedeemConfirmFlowsEvent.class, captured);

        RedeemConfirmFlowsEvent event = (RedeemConfirmFlowsEvent) captured;
        Assertions.assertEquals(String.join(SLASH, accountInfo.getHash(), String.valueOf(accountInfo.getId())), event.accountId);
        Assertions.assertEquals(new BigDecimal(redeem.getAmount()), event.amount);
    }
}
